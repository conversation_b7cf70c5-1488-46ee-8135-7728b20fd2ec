let fs=require("fs"),args=process.argv.slice(2),packageList=["./package.json"],dependencies=["asite-styleguide"],currentBranch=getBranchName(args[0]);function modifyUrl(e){return e?.split("#")[0]}function getBranchName(e){switch(e){case"QA-SNAPSHOT":return"QA";case"SNAPSHOT":return"master";default:return e=e&&"release/RELEASE-"+e.split("-").slice(0,3).join("-")}}currentBranch||(console.error("No current branch found. Exiting."),process.exit(1)),console.log("New Branch Name: ",currentBranch),packageList.forEach(n=>{try{var e=fs.readFileSync(n,"utf8");let r=JSON.parse(e);r.dependencies=r.dependencies||{},dependencies.forEach(e=>{r.dependencies[e]?(console.log("From: ",r.dependencies[e]),r.dependencies[e]=modifyUrl(r.dependencies[e])+"#"+currentBranch,console.log("To: ",r.dependencies[e])):(console.error(e+"is not found in "+n),process.exit(1))});try{fs.writeFileSync(n,JSON.stringify(r,null,2)),console.log("updated: ",n)}catch(e){console.error("Error while writing file: ",e.message,n),process.exit(1)}}catch(e){console.error("Error while reading file: ",e.message,n),process.exit(1)}});