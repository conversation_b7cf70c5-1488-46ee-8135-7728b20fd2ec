import type { Preview } from "@storybook/angular";
import { setCompodo<PERSON><PERSON><PERSON> } from "@storybook/addon-docs/angular";
import { INITIAL_VIEWPORTS } from "@storybook/addon-viewport";
import doc<PERSON><PERSON> from "../documentation.json";
setCompodoc<PERSON><PERSON>(doc<PERSON>son);
const preview: Preview = {
  parameters: {
    actions: { argTypesRegex: "^on[A-Z].*" },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
    docs: { inlineStories: true },
    viewport: {
      viewports: INITIAL_VIEWPORTS
    },
  },
};
export default preview;