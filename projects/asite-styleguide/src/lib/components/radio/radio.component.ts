import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { RadioPosition, radioPositionEnum, radioGroupItem } from './radio.model';
import { FormsModule } from '@angular/forms';
import { MatRadioButton, MatRadioModule } from '@angular/material/radio';
@Component({
  selector: 'lib-radio',
  standalone: true,
  imports: [CommonModule,FormsModule, MatRadioModule],
  templateUrl: './radio.component.html',
  styleUrl: './radio.component.scss'
})
export class RadioComponent {
  /**
  * Represents the currently selected option in the radio component.
  * This property holds the value of the selected radio button.
  */
  @Input() selectedValue: string = '';

  /**
   * An array of radio group items that will be used to populate the radio buttons in the component.
   * Each item in the array should conform to the `radioGroupItem` interface.
   *
   * @type {radioGroupItem[]}
   */
  @Input() radioGroupItem: radioGroupItem[] = [];


  /**
   * Specifies the orientation of the radio buttons.
   *
   * @type {RadioPosition}
   * @default radioPositionEnum.Horizontal
   */
  @Input() orientation: RadioPosition = radioPositionEnum.Horizontal;

  /**
   * @description Boolean to make radio selection toggle
   * @type {boolean}
   * @memberof RadioComponent
   */
  @Input() allowDeselect: boolean = false;

  /**
   * @description Properties to show error below of radio group
   * @type {string}
   * @memberof RadioComponent
   */
  @Input() radioState: string = 'default';
  @Input() infoText: string = '';

  /**
   * The label for the radio component.
   * This will be displayed next to the radio button.
   */
  @Input() label: string = '';


  /**
   * Indicates whether the radio button is disabled.
   * When set to `true`, the radio button will be rendered in a disabled state,
   * preventing user interaction.
   *
   * @default false
   */
  @Input() isDisabled: boolean = false;


  /**
   * Indicates whether the radio button is required.
   * When set to true, the radio button must be selected in order to complete the form.
   *
   * @default false
   */
  @Input() isRequired: boolean = false;


  /**
   * Event emitter that emits when a radio button is selected.
   * The emitted event contains the selected value.
   */
  @Output() radioSelected = new EventEmitter<any>();

  /**
   * Handles the change event when a radio button selection is made.
   * This method logs the currently selected option to the console and updates the
   * checked state of each radio group item based on the selected option. If an item
   * matches the selected option, it is marked as checked and an event is emitted
   * with the selected item. All other items are marked as unchecked.
   */

  onSelectionChange() {
    if(this.allowDeselect) {
      return;
    }

    if (this.selectedValue) {
      const selectedItem = this.radioGroupItem?.find(item => item?.value === this.selectedValue);
      if (selectedItem) {
        this.radioSelected.emit(selectedItem);
      }
    }
  }

  /**
   * @description Toggle radio selection
   * @param {*} radioData
   * @memberof RadioComponent
   */
  onRadioBtnClicked(radioData) {
    if(this.isDisabled || !this.allowDeselect) {
      return;
    }

    if (this.selectedValue) {
      this.selectedValue = '';
      this.radioSelected.emit(this.selectedValue);
    } else {
      this.selectedValue = radioData.value;
      this.radioSelected.emit(this.selectedValue);
    }
  }
}
