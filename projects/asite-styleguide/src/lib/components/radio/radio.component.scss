@import "../../../../assets/scss/styles.scss";

.radio-wrapper {
  font-family: $fontFamily;
  font-style: normal;
  line-height: normal;
  display: flex;
  flex-direction: column;

  .radio-label {
    color: var(--lib-color-black, #0C1315);
    margin: 0;

    span.field-error {
      color: var(--lib-color-red-2, #8E0000);
    }
  }

  .radio-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .status-text {
      font-size: 12px;
      font-weight: 400;
      padding-right: 16px;
    }
  }

  .horizontal {
    display: flex;
    gap: 8px;
  }

  .vertical {
    display: flex;
    flex-direction: column;
  }

  .radio-helper-info {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    padding: 0 2px;
    font-family: $fontFamily;

    &.info {
      color: var(--lib-color-tertiary, #1D1878);
    }

    &.error {
      color: var(--lib-color-red-2, #8E0000);
    }
  }
}
