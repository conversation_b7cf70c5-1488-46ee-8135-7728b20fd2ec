<div class="radio-wrapper" *ngIf="radioGroupItem">
  <p *ngIf="label" class="label-large radio-label">{{label}} <span [ngClass]="{'field-error':isRequired}" *ngIf="isRequired || false">&#42;</span></p>
  <mat-radio-group [(ngModel)]="selectedValue" [class]="orientation" (change)="onSelectionChange()" [required]="isRequired">
    @for ( radioData of radioGroupItem; track radioData) {
      @if(!radioData.checkedAndDisabled){
        <mat-radio-button class="asite-ui-radio" [value]="radioData.value"
        [disabled]="isDisabled || radioData.isDisabled" [name]="radioData.name" (click)="onRadioBtnClicked(radioData)"> 
          <div class="radio-content">
            <span>{{ radioData.title }}</span>
            @if(radioData.rightSideText){
              <span [style.color]="radioData.rightSideTextColor" class="status-text">{{ radioData.rightSideText }}</span>
          }
          </div>
      </mat-radio-button>
      }
      @else if (radioData.checkedAndDisabled){
        <mat-radio-group>
          <mat-radio-button class="asite-ui-radio" [checked]="true" [value]="radioData.value" [disabled]="true" [name]="radioData.name">
            <div class="radio-content">
              <span>{{ radioData.title }}</span>
              @if(radioData.rightSideText){
                <span [style.color]="radioData.rightSideTextColor" class="status-text">{{ radioData.rightSideText }}</span>
              }
            </div>
          </mat-radio-button>
        </mat-radio-group>
      }
    }
  </mat-radio-group>
  <div class="radio-helper-info" *ngIf="infoText && radioState" [ngClass]="{'error': (radioState === 'error'), 'info': (radioState === 'info')}">
    <p>{{infoText}}</p>
    <i *ngIf="radioState === 'info'" class="iconoir-info-circle"></i>
    <i *ngIf="radioState === 'error'" class="iconoir-warning-circle"></i>
  </div>
</div>
