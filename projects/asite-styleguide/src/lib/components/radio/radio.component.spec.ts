import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RadioComponent } from './radio.component';
import { radioGroupItem, radioPositionEnum } from './radio.model';

describe('RadioComponent', () => {
  let component: RadioComponent;
  let fixture: ComponentFixture<RadioComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RadioComponent]
    })
      .compileComponents();

    fixture = TestBed.createComponent(RadioComponent);
    component = fixture.componentInstance;
    component.radioGroupItem = [
      { id: "radio1", title: 'Radio', name: "radio_name0", value: "radio" },
      { id: "radio2", title: 'Radio', name: "radio_name1", value: "radio1" }
    ];
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should update the radioData when checked and emit the selected value is passed', () => {
    // Setup
    const radioData = { id: "radio1", title: 'Radio', name: "radio_name0", value: "radio" };
    spyOn(component.radioSelected, 'emit');

    // Execution
    component.selectedValue = "radio";
    component.onSelectionChange();

    // Assertion
    expect(component.radioSelected.emit).toHaveBeenCalledWith(radioData);
  });

  it('should not update the radioData when checked and emit the selected value is not passed', () => {
    // Setup
    const radioData = { id: "radio1", disabled: true, checked: false, isRequired: false, title: 'Radio', name: "radio_name0", value: "" };
   spyOn(component.radioSelected, 'emit');
    // Execution
    component.selectedValue = "";
    component.onSelectionChange();

    // Assertion
    expect(component.radioSelected.emit).not.toHaveBeenCalled();
  });

  it('should emit when radio is selected', () => {
    const radioItems: radioGroupItem[] = [
      { id: "radio1", title: 'Radio', name: "radio_name0", value: "radio1" },
      { id: "radio2", title: 'Radio', name: "radio_name0", value: "radio2" },
    ];
    component.radioGroupItem = radioItems;
    spyOn(component.radioSelected, 'emit');
    component.selectedValue = "radio1";
    component.onSelectionChange();
    expect(component.radioSelected.emit).toHaveBeenCalledWith(radioItems[0]);
  });

  it('should have default orientation as Horizontal', () => {
    expect(component.orientation).toBe(radioPositionEnum.Horizontal);
  });

  it('should have default label as empty string', () => {
    expect(component.label).toBe('');
  });

  it('should have default isDisabled as false', () => {
    expect(component.isDisabled).toBe(false);
  });

  it('should have default isRequired as false', () => {
    expect(component.isRequired).toBe(false);
  });

  it('should disable the radio buttons when isDisabled is true', () => {
    component.isDisabled = true;
    fixture.detectChanges();
    const radioButtons = fixture.nativeElement.querySelectorAll('mat-mdc-radio-disabled');
    radioButtons.forEach((radioButton: any) => {
      expect(radioButton.disabled).toBe(true);
    });
  });

  it('should not emit if allowDeselect is false', () => {
    component.allowDeselect = false;
    component.selectedValue = '1';
    spyOn(component.radioSelected, 'emit');

    component.onRadioBtnClicked({ value: '2' });

    expect(component.selectedValue).toBe('1');
    expect(component.radioSelected.emit).not.toHaveBeenCalled();
  });

  it('should not emit if allowDeselect is true', () => {
    component.allowDeselect = true;
    component.selectedValue = '1';
    spyOn(component.radioSelected, 'emit');

    component.onSelectionChange();

    expect(component.selectedValue).toBe('1');
    expect(component.radioSelected.emit).not.toHaveBeenCalled();
  });

  it('should deselect if selectedValue is set and allowDeselect is true', () => {
    component.allowDeselect = true;
    component.selectedValue = '1';
    spyOn(component.radioSelected, 'emit');

    component.onRadioBtnClicked({ value: '2' });

    expect(component.selectedValue).toBe('');
    expect(component.radioSelected.emit).toHaveBeenCalledWith('');
  });

  it('should select the radio value if none is selected and allowDeselect is true', () => {
    component.allowDeselect = true;
    component.selectedValue = '';
    spyOn(component.radioSelected, 'emit');

    component.onRadioBtnClicked({ value: '2' });

    expect(component.selectedValue).toBe('2');
    expect(component.radioSelected.emit).toHaveBeenCalledWith('2');
  });

  it('should render rightSideText when provided in radioGroupItem', () => {
    const radioItems: radioGroupItem[] = [
      { id: "radio1", title: 'Radio', name: "radio_name0", value: "radio1",rightSideText: "Extra Info" },
    ];
    component.radioGroupItem = radioItems;
    fixture.detectChanges();
    const rightSideText = fixture.nativeElement.querySelector('.status-text');
    expect(rightSideText).toBeTruthy();
    expect(rightSideText.textContent).toContain('Extra Info');
  });

  it('should disable individual radio button when isDisabled is true in radioGroupItem', () => {
    const radioItems: radioGroupItem[] = [
      { id: "radio1", title: 'Radio', name: "radio_name0", value: "radio1",isDisabled: true },
    ];
    component.radioGroupItem = radioItems;
    fixture.detectChanges();
    const radioButton = fixture.nativeElement.querySelector('mat-radio-button');
    const input = radioButton.querySelector('input[type="radio"]');
    expect(input).toBeTruthy();
    expect(input.disabled).toBeTrue();
  });
});
