export enum radioPositionEnum {
  Horizontal = 'horizontal',
  Vertical = 'vertical',
}

export type RadioPosition = radioPositionEnum.Horizontal | radioPositionEnum.Vertical;

/**
 * @description radio Item
 * @export
 * @interface radioGroupItem
 */
export interface radioGroupItem {
  id: string;
  title: string;
  name: string;
  value: string;
  isDisabled?: boolean;
  rightSideText?: string;
  rightSideTextColor?: string;
}
