<div class="main">
  <ng-container *ngFor="let tab of tabDataType; index as i">
    <ng-container *ngIf="i < tabCount && !tab.hide">
      <div class="container-main"
        [ngClass]="[ !tab.disabled ? !defaultSelectedTab && defaultSelectedTab != 0 && i == 0 ? 'selected' : i === defaultSelectedTab - 1 ? 'selected' : 'default' : 'default' ]"
        [class.disabled]="tab.disabled">
        <div class="tab" [class.disabled]="tab.disabled" *ngIf="tab.label || icon" (click)="selectTab(tab)">
          <div class="container"
            [ngClass]="[tab.label && (icon || tab.icon) ? 'withIcon' : tab.label && !icon ? 'labelOnly' : 'iconOnly']"
            [class]="iconPosition">
            <i [class]="icon || tab.icon" *ngIf="icon || tab.icon"></i>
            <span class="label" *ngIf="tab.label != '' || icon == ''">{{ tab.label }}</span>
          </div>
        </div>
        <div
          [ngClass]=" (tab.id === defaultSelectedTab || i == 0 || defaultSelectedTab - 1 == i) && !tab.disabled ? 'bottom-show' : 'bottom-hide'">
        </div>
      </div>
    </ng-container>
  </ng-container>

  <ng-container *ngFor="let moreTab of getMoreTabs()">
    <div class="tab" (click)="selectTab(moreTab)" [ngClass]="['default']">
      <div class="container">
        <span class="label">{{ moreTab.label }}</span>
        <i class="iconoir-nav-arrow-down"></i>
      </div>
    </div>
  </ng-container>
</div>