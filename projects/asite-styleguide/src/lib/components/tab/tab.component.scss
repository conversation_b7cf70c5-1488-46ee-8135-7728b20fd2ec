@import '../../../../assets/scss/styles.scss';

.main {
    border-bottom: 1px solid var(--lib-color-gray-6, #E9E9E9);
    width: 100%;
    display: flex;
    justify-content: center;

    .container-main {

        &.default {
            display: inline-flex;
            flex-direction: column;
            flex-shrink: 0;
            border: none;

            &.right {
                flex-direction: row-reverse;
            }

            .bottom-hide {
                height: 6px;
            }
        }

        &.selected {
            border-radius: 10px 10px 0px 0px;
            color: var(--lib-color-primary, #4940D7);
            font-weight: 700;

            .bottom-show {
                height: 6px;
                border-radius: 8px 8px 0px 0px;
                background: var(--lib-color-primary, #4940D7);
                cursor: pointer;
            }

        }

        &.disabled {
            color: var(--lib-color-gray-4, #8D8D8D);
            cursor: not-allowed;

            .bottom-show {
                height: 6px;
                border-radius: 8px 8px 0px 0px;
                background: var(--lib-color-gray-4, #8D8D8D);
            }

            &.selected {
                background: var(--lib-color-white, #FFFFFF);
            }

            &:hover {
                background: none;
            }
        }

    }

    .tab {
        color: var(--lib-color-black, #0C1315);
        text-align: center;
        font-feature-settings: 'clig' off, 'liga' off;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: $fontFamily;
        font-size: 0.875rem;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        height: 36px;
        border-bottom: var(--lib-color-gray-6, #E9E9E9);
        text-wrap: nowrap;
        cursor: pointer;

        &:hover {
            border-radius: 10px;
            background: var(--lib-color-purple-6, #CFCDF4);
        }

        &:active {
            border-radius: 10px;
            background: var(--lib-color-purple-6, #9892F2);
        }

        &.disabled {
            color: var(--lib-color-gray-4, #8D8D8D);
            cursor: not-allowed;

            .bottom-show {
                height: 6px;
                border-radius: 8px 8px 0px 0px;
                background: var(--lib-color-gray-4, #8D8D8D);
            }

            &.selected {
                background: var(--lib-color-white, #FFFFFF);
            }

            &:hover {
                background: none;
            }
        }

        .container {
            padding: 0px 16px;
            justify-content: center;
            align-items: center;
            display: flex;

            &.withIcon {
                gap: 8px;
            }

            &.right {
                flex-direction: row-reverse;
            }
        }

        &.withIcon {
            gap: 8px;
        }

        i {
            font-size: 1.25rem;
        }
    }
}