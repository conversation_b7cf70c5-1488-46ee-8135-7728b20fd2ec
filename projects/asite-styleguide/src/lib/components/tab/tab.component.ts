import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IconPosition, TabData, TabDataType } from './tab.model';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '../button/button.component';

@Component({
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  selector: 'lib-tab',
  templateUrl: './tab.component.html',
  styleUrls: ['./tab.component.scss'],
})
export class TabComponent {
  /**
   * @description icon
   * @memberof TabComponent
   */
  @Input() icon: string = '';

  /**
   * @description Position of Icon
   * @memberof TabComponent
   */
  @Input() iconPosition: IconPosition | null = null;

  /**
   * @description Array containing Tab items.
   * @memberof TabComponent
   */
  @Input() tabDataType: TabData[] = [];

  /**
   * @description Default selected tab
   * @memberof TabComponent
   */
  @Input() defaultSelectedTab: number;

  visibleTabs: TabDataType | undefined;

  /**
   * @description Visible Tabs count
   * @memberof TabComponent
   */
  @Input() tabCount: number;

  ngOnInit() {
    this.updateVisibleTabs();
    window.addEventListener('resize', () => {
      this.updateVisibleTabs();
    });
  }

  ngOnDestroy() {
    window.removeEventListener('resize', () => {});
  }

  /**
   * @description Update the Tabs to be Display according to screen size
   * @memberof TabComponent
   */
  updateVisibleTabs() {
    const screenWidth = window.innerWidth;
    const maxTabs = Math.floor(screenWidth / 100); // Adjust 100 based on your styling and spacing
    this.tabCount = maxTabs - 1;
    this.visibleTabs = this.tabDataType.slice(0, maxTabs - 1); // Leave one slot for "More" tab
  }

  /**
   * @description Adding the More Tab
   * @memberof TabComponent
   */
  getMoreTabs() {
    const moreTabs = this.tabDataType.slice(this.visibleTabs.length);
    return moreTabs.length > 0 ? [{ id: 0, label: 'More', disabled: false }] : [];
  }

  /**
   * @description Tabs Swaping
   * @param {*} tab1Id
   * @param {*} tab2Id
   * @memberof TabComponent
   */
  swapTabs(tab1Id: number, tab2Id: number) {
    const tab1Index = this.tabDataType.findIndex((tab) => tab.id === tab1Id);
    const tab2Index = this.tabDataType.findIndex((tab) => tab.id === tab2Id);

    if (tab1Index !== -1 && tab2Index !== -1) {
      // Swap the elements using destructuring assignment
      [this.tabDataType[tab1Index], this.tabDataType[tab2Index]] = [this.tabDataType[tab2Index], this.tabDataType[tab1Index]];
    }
  }

  /**
   * @description Tab Selection
   * @param {*} tab
   * @memberof TabComponent
   */
  selectTab(tab) {
    // swap will happen, if "More" tab is available and also if the tabs below more are selected.
    if (this.tabCount < this.tabDataType.length && !this.visibleTabs.some(x => x.id === tab.id)) {
      this.swapTabs(tab.id, this.tabDataType[this.tabCount - 1].id);
      this.visibleTabs = this.tabDataType.slice(0, this.tabCount);
    }

    if (!tab.disabled) {
      this.defaultSelectedTab = tab.id;
      this.tabSelected.emit(tab);
    }
  }

  @Output() tabSelected = new EventEmitter<TabData>();
}
