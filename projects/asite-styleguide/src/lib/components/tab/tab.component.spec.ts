import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TabComponent } from './tab.component';
import {
  IconPosition,
  IconPositionEnum,
  TabData,
} from './tab.model';

describe('TabComponent', () => {
  let component: TabComponent;
  let fixture: ComponentFixture<TabComponent>;

  beforeEach(async () => {
    fixture = TestBed.createComponent(TabComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default first tab selected', () => {
    expect(component.defaultSelectedTab).toBeUndefined();
  });

  it('should set defaultSelectedTab', () => {
    const defaultSelectedTab = 1;
    component.defaultSelectedTab = defaultSelectedTab;
    expect(component.defaultSelectedTab).toEqual(defaultSelectedTab);
  });

  it('should have default value of an empty string for icon property', () => {
    expect(component.icon).toEqual('');
  });

  it('should set a valid icon', () => {
    const validIcon = 'iconoir-lens';
    component.icon = validIcon;
    expect(component.icon).toEqual(validIcon);
  });

  it('should have default value of null for iconPosition property', () => {
    expect(component.iconPosition).toBeNull();
  });

  it('should set Icon Position property to left', () => {
    const validIconPosition: IconPosition = IconPositionEnum.Left;
    component.iconPosition = validIconPosition;
    expect(component.iconPosition).toEqual(validIconPosition);
  });

  it('should set Icon Position property to right', () => {
    const validIconPosition: IconPosition = IconPositionEnum.Right;
    component.iconPosition = validIconPosition;
    expect(component.iconPosition).toEqual(validIconPosition);
  });

  it('should update visible tabs on initialization', () => {
    component.tabDataType = [
      { id: 1, label: 'Tab 1', disabled: false },
      { id: 2, label: 'Tab 2', disabled: false },
    ];

    spyOn(component, 'updateVisibleTabs').and.callThrough();

    component.ngOnInit();

    expect(component.updateVisibleTabs).toHaveBeenCalled();
    expect(component.visibleTabs.length).toBeGreaterThan(0);
  });

  it('should emit tabSelected event when a tab is selected', () => {
    const testTab: TabData = { id: 1, label: 'Test Tab', disabled: false };
    spyOn(component.tabSelected, 'emit');

    component.selectTab(testTab);

    expect(component.tabSelected.emit).toHaveBeenCalledWith(testTab);
  });

  it('should call updateVisibleTabs on resize event', () => {
    spyOn(component, 'updateVisibleTabs');

    component.ngOnInit();

    const resizeEvent = new Event('resize');
    window.dispatchEvent(resizeEvent);

    expect(component.updateVisibleTabs).toHaveBeenCalled();
  });

  // Cleanup
  afterEach(() => {
    fixture.destroy();
  });
});

describe('Icon Position Enums', () => {
  it('should have correct enum values', () => {
    expect(IconPositionEnum.Left).toEqual('left');
    expect(IconPositionEnum.Right).toEqual('right');
  });
});

describe('Tab tabSelected onClick Output', () => {
  let component: TabComponent;
  let fixture: ComponentFixture<TabComponent>;

  beforeEach(() => {
    fixture = TestBed.createComponent(TabComponent);
    component = fixture.componentInstance;
  });

  it('should emit tabSelected event when a tab is selected', () => {
    const tabData: TabData = {
      id: 1,
      label: 'Tab 1',
      disabled: false,
    };
    let emittedTabData: TabData | undefined;

    // Subscribe to the event emitter
    component.tabSelected.subscribe((data) => {
      emittedTabData = data;
    });

    component.tabSelected.emit(tabData);
    expect(emittedTabData).toEqual(tabData);
  });

  it('should call selectTab Function', () => {
    component.tabDataType = [
      { id: 1, label: 'Tab 1', disabled: false },
      { id: 2, label: 'Tab 2', disabled: false },
    ];
    component.visibleTabs = [{ id: 3, label: 'Tab 3', disabled: false }];
    component.tabCount = 1;
    component.selectTab(component.tabDataType);

    spyOn(component, 'swapTabs').and.callThrough();

    expect(component.visibleTabs.length).toEqual(1);
  });

  it('should call swapTab Function', () => {
    component.tabDataType = [
      { id: 1, label: 'Tab 1', disabled: false },
      { id: 2, label: 'Tab 2', disabled: false },
      { id: 3, label: 'Tab 3', disabled: false },
    ];
    component.swapTabs(1, 3);
    expect(component.swapTabs).toBeTruthy();
  });
});
