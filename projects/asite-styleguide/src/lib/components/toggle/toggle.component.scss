@import "../../../../assets/scss/styles.scss";

.toggle-wrapper {
  font-family: $fontFamily;
  font-style: normal;
  line-height: normal;

  .input-wrapper {
    display: flex;
    gap: 8px;
    align-items: center;

    & > label {
      font-size: 14px;
      font-weight: 400;
      line-height: 14px;
      color: var(--lib-color-black, #0c1315);
    }

    .on-off-toggle {
      width: auto;
      height: 24px;
      position: relative;
      cursor: pointer;

      .toggle-input {
        position: absolute;
        opacity: 0;
        margin: 0;
        width: 100%;
        height: 100%;
        z-index: -1;

        &[disabled]:disabled + .toggle-slider {
          background-color: var(--lib-color-gray-6, #e9e9e9);
          .slider-text {
            color: var(--lib-color-gray-4, #8d8d8d);
          }
          cursor: not-allowed;

          &:before {
            background-color: var(--lib-color-gray-4, #8d8d8d);
            background-image: url(../../../../assets/svg/disabledCross.svg);
          }

          &:after {
            color: var(--lib-color-gray-4, #8d8d8d);
          }
        }
      }

      .toggle-input:checked + .toggle-slider {
        background-color: var(--Product-Primary---Lilac, #4940d7);

        &:before {
          background-color: var(--Neutral-White, #fff);
          background-image: url(../../../../assets/svg/tick.svg);
          left: calc(100% - 20px);
        }

        &:after {
          padding-left: 5px;
        }
      }

      .toggle-input:checked:hover + .toggle-slider {
        background-color: var(--lib-color-purple-3, #3d36b4);

        &:before {
          background-image: url(../../../../assets/svg/hoverTick.svg);
        }
      }

      .toggle-input:checked:active + .toggle-slider {
        background-color: var(--lib-color-purple-3, #352f9d);

        &:before {
          background-image: url(../../../../assets/svg/activeTick.svg);
        }
      }

      .toggle-input[disabled]:checked + .toggle-slider {
        background-color: var(--lib-color-gray-6, #e9e9e9);

        &:before {
          background-color: var(--lib-color-gray-4, #8d8d8d);
          background-image: url(../../../../assets/svg/disabledCross.svg);
        }
      }
      .toggle-slider {
        width: auto;
        height: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: var(--lib-color-gray-4, #8d8d8d);
        transition: background-color 0.4s;
        position: relative;
        border-radius: 20px;
        cursor: pointer;

        .slider-text {
          font-size: 12px;
          color: var(--lib-color-white, #fff);
          padding: 0 5px;
          transition: all 0.4s;
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          &.text-left {
            padding-right: 24px;
          }

          &.text-right {
            padding-left: 24px;
          }
        }

        &:before {
          content: "";
          background-color: var(--Neutral-White, #fff);
          background-image: url(../../../../assets/svg/cross.svg);
          background-position: 50%;
          background-repeat: no-repeat;
          left: 3px;
          width: 18px;
          height: 18px;
          position: absolute;
          transition: 0.4s;
          border-radius: 100%;
        }

        &:after {
          line-height: 21px;
          text-transform: capitalize;
          font-size: 12px;
          color: var(--Neutral-White, #fff);
          padding-left: 24px;
          transition: all 0.4s;
          padding-right: 24px;
          text-align: right;
        }
      }

      .toggle-slider:hover {
        background-color: var(--lib-color-gray-3, #575757);

        &:before {
          background-image: url(../../../../assets/svg/hoverCross.svg);
        }
      }

      .toggle-slider:active {
        background-color: var(--lib-color-gray-2, #414141);

        &:before {
          background-image: url(../../../../assets/svg/activeCross.svg);
        }
      }
    }
  }
}
