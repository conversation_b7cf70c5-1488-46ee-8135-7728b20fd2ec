import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToggleComponent } from './toggle.component';

describe('ToggleComponent', () => {
  let component: ToggleComponent;
  let fixture: ComponentFixture<ToggleComponent>;

  beforeEach(() => {
    fixture = TestBed.createComponent(ToggleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

describe('ToggleComponent Default Values', () => {
  let component: ToggleComponent;
  let fixture: ComponentFixture<ToggleComponent>;

  beforeEach(() => {
    fixture = TestBed.createComponent(ToggleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default values for properties', () => {
    expect(component.onText).toEqual('');
    expect(component.offText).toEqual('');
    expect(component.label).toEqual('');
    expect(component.labelPosition).toEqual('');
    expect(component.disabled).toEqual(false);
    expect(component.checked).toEqual(false);
    expect(component.isRequired).toEqual(false);
  });
});

describe('ToggleComponent onText Input', () => {
  let component: ToggleComponent;
  let fixture: ComponentFixture<ToggleComponent>;

  beforeEach(() => {
    fixture = TestBed.createComponent(ToggleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be string', () => {
    expect(typeof component.onText).toBe('string');
  });

  it('should update the toggle text when the onText input changes', () => {
    const mockOnText = 'On';
    component.onText = 'On';
    fixture.detectChanges();
    expect(component.onText).toContain(mockOnText);
  });
});

describe('ToggleComponent offText Input', () => {
  let component: ToggleComponent;
  let fixture: ComponentFixture<ToggleComponent>;

  beforeEach(() => {
    fixture = TestBed.createComponent(ToggleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be string', () => {
    expect(typeof component.offText).toBe('string');
  });

  it('should update the toggle text when the onText input changes', () => {
    const mockoffText = 'Off';
    component.offText = 'Off';
    fixture.detectChanges();
    expect(component.offText).toContain(mockoffText);
  });
});

describe('ToggleComponent label Input', () => {
  let component: ToggleComponent;
  let fixture: ComponentFixture<ToggleComponent>;

  beforeEach(() => {
    fixture = TestBed.createComponent(ToggleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be string', () => {
    expect(typeof component.label).toBe('string');
  });

  it('should update the label text when the label input changes', () => {
    const mockLabel = 'Custom Label';
    component.label = 'Custom Label';
    fixture.detectChanges();
    expect(component.label).toContain(mockLabel);
  });
});

describe('ToggleComponent labelPosition Input', () => {
  let component: ToggleComponent;
  let fixture: ComponentFixture<ToggleComponent>;

  beforeEach(() => {
    fixture = TestBed.createComponent(ToggleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be string', () => {
    expect(typeof component.labelPosition).toBe('string');
  });

  it('should have default Value labelPosition', () => {
    expect(component.labelPosition).toBe('');
  });

  it('should update the label position of toggle when the labelPosition is left', () => {
    const mocklabelPosition = 'left';
    component.labelPosition = 'left';
    fixture.detectChanges();
    expect(component.labelPosition).toContain(mocklabelPosition);
  });

  it('should update the label position of toggle when the labelPosition is right', () => {
    const mocklabelPosition = 'right';
    component.labelPosition = 'right';
    fixture.detectChanges();
    expect(component.labelPosition).toContain(mocklabelPosition);
  });
});

describe('Toggle disabled Input', () => {
  let component: ToggleComponent;
  beforeEach(() => {
    component = new ToggleComponent();
  });

  it('should have default value of false for disabled property', () => {
    expect(component.disabled).toBe(false);
  });

  it('should set disabled property to true', () => {
    component.disabled = true;
    expect(component.disabled).toBe(true);
  });

  it('should handle setting disabled property to false', () => {
    component.disabled = true;
    component.disabled = false;
    expect(component.disabled).toBe(false);
  });
});

describe('Toggle isRequired Input', () => {
  let component: ToggleComponent;
  beforeEach(() => {
    component = new ToggleComponent();
  });

  it('should have default value false for isRequired', () => {
    expect(component.isRequired).toBe(false);
  });

  it('should set isRequired property to true', () => {
    component.isRequired = true;
    expect(component.isRequired).toBe(true);
  });

  it('should handle setting isRequired property to false', () => {
    component.isRequired = true;
    component.isRequired = false;
    expect(component.isRequired).toBe(false);
  });
});

describe('Toggle onToggle Output', () => {
  let component: ToggleComponent;
  let fixture: ComponentFixture<ToggleComponent>;
  beforeEach(() => {
    fixture = TestBed.createComponent(ToggleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should emit an event when clicked', () => {
    spyOn(component.checkedChange, 'emit');
    const event = false;
    component.checkedChange.emit(event);
    expect(component.checkedChange.emit).toHaveBeenCalledWith(event);
  });
});

describe('Toggle toggleChecked method', () => {
  let component: ToggleComponent;
  let fixture: ComponentFixture<ToggleComponent>;
  beforeEach(() => {
    fixture = TestBed.createComponent(ToggleComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should emit an event', () => {
    spyOn(component.checkedChange, 'emit');
    expect(component.checked).toBeFalse();
    component.toggleChecked();
    expect(component.checked).toBeTrue();
    expect(component.checkedChange.emit).toHaveBeenCalledWith(true);

    component.toggleChecked();
    expect(component.checked).toBeFalse();
    expect(component.checkedChange.emit).toHaveBeenCalledWith(false);
  });

  it('should emit an event', () => {
    spyOn(component.checkedChange, 'emit');
    const event = false;
    component.checkedChange.emit(event);
    expect(component.checkedChange.emit).toHaveBeenCalledWith(event);
  });

  it('should emit object with data when data is present', () => {
    component.data = { id: 123 };
    component.checked = false;
    component.toggleChecked();
    expect(component.checked).toBeTrue();
  });
});
