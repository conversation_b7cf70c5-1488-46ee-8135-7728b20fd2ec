import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'lib-toggle',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './toggle.component.html',
  styleUrl: './toggle.component.scss',
})
export class ToggleComponent {
  @Input() onText: string = '';

  @Input() data: any = undefined;

  @Input() offText: string = '';

  @Input() label: string = '';

  @Input() title: boolean = false;

  @Input() uniqueId: string = '';

  @Input() labelPosition: string = '';

  @Input() disabled: boolean = false;

  @Input() checked: boolean = false;

  @Input() isRequired: boolean = false;

  /**
   * Emits an event when the value of the checkbox changes.
   */
  @Output() checkedChange = new EventEmitter<any>();

  toggleChecked(): void {
    this.checked = !this.checked;
    
    if (this.data) {
      this.checkedChange.emit({
        data: this.data,
        checked: this.checked,
        toggleChecked: this.toggleChecked.bind(this)
      });
    } else {
      this.checkedChange.emit(this.checked);
    }

  }
}
