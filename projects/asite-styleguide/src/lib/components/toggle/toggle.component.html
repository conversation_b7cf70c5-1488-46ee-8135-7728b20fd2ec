<div class="toggle-wrapper">
  <div class="input-wrapper">
    <label *ngIf="label && labelPosition == 'left'" for="{{uniqueId}}">{{label}} <span *ngIf="isRequired || false"
        style="color:red">&#42;</span></label>
    <div class="on-off-toggle">
      <input class="toggle-input" type="checkbox" id={{uniqueId}} [required]="isRequired" [disabled]="disabled"
        [ngModel]="checked" (ngModelChange)="toggleChecked()" />
      <label for="{{uniqueId}}" class="toggle-slider" [title]="title ? (checked ? onText : offText) : ''">
        <div class="slider-text" [ngClass]="{'text-left': checked, 'text-right': !checked}">
          {{ checked ? onText : offText }}
        </div>
      </label>
    </div>
    <label *ngIf="label && labelPosition == 'right'" class="rightLabel" for="{{uniqueId}}">{{label}} <span
        *ngIf="isRequired || false" style="color:red">&#42;</span></label>
  </div>
</div>