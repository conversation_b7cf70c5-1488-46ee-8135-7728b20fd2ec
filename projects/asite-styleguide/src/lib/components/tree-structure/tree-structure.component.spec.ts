import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CommonModule } from '@angular/common';
import { TreeStructureComponent } from './tree-structure.component';
import { SearchBarComponent } from '../index';

describe('TreeStructureComponent', () => {
  let component: TreeStructureComponent;
  let fixture: ComponentFixture<TreeStructureComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TreeStructureComponent]
    })
      .compileComponents();

    fixture = TestBed.createComponent(TreeStructureComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default input values', () => {
    expect(component.treeVisibleData).toEqual([]);
    expect(component.showCount).toBe(false);
    expect(component.showFolderIcon).toBe(true);
    expect(component.expandableIcon).toBe(false);
  });

  it('should emit treeChildData when fetchChildren is called', () => {
    spyOn(component.treeChildData, 'emit');
    const item: any = [{ "iProjectId": 0, "dcId": 1, "projectName": "Lighthouse Architecture", "projectID": "2110708$$VQaLQC", "parentId": -1, "isWorkspace": 1, "isTemplate": false, "isCloned": false, "isWatching": false, "isFavourite": true, "hasSubFolder": true, "childfolderTreeVOList": [{ "isPublic": false, "isFavourite": true, "childfolderTreeVOList": [], "folder_title": "Main Folder", "folderId": "71442083", "projectId": "2110708", "hasSubFolder": true, "parentFolderId": 0, "folderPath": "Lighthouse Architecture\\Main Folder", "isActive": 1 }] }, { "dcId": 1, "projectName": "Tree Structure 1", "projectID": "2110701", "parentId": -1, "isWorkspace": 1, "hasSubFolder": true, "childfolderTreeVOList": [{ "isPublic": false, "isFavourite": true, "childfolderTreeVOList": [], "folder_title": "Structure 1", "folderId": "71442089", "projectId": "2110701", "hasSubFolder": false, "parentFolderId": 0, "folderPath": "Tree Structure 1/Structure 1", "isActive": 1 }], "isFavourite": true }, { "dcId": 1, "projectName": "Tree Structure 2", "projectID": "2110702", "parentId": -1, "isWorkspace": 1, "hasSubFolder": false, "isFavourite": true }];
    component.fetchChildren(item);
    expect(component.treeChildData.emit).toHaveBeenCalled();
    expect(item[0].hasSubFolder).toBe(true);

    // should set isFetching true if isFetchFromRemote is true
    component.isFetchFromRemote = true;
    component.fetchChildren(item[0]);
    expect(item[0].isFetching).toBeTrue();
  });

  it('should emit searchItem when searchFolders is called', () => {
    spyOn(component.searchItem, 'emit');
    const item = "test";// { name: 'test' };
    component.searchFolders(item);
    expect(component.searchItem.emit).toHaveBeenCalled();
  });

  it('should select tree item correctly', () => {
    const item = { folderId: '123', isTemplate: false, isAdminAccess: true, selected: false };
    component.selectTreeItem(new Event('click'), item);
    expect(component.selectedFolderId).toBe('123');
    //expect(component.currentSelected).toBe(item);
    expect(item.selected).toBe(false);
  });

  it('should toggle sub item correctly', () => {
    const item = { open: false };
    const mockEvent = new MouseEvent('click');
    spyOn(component, 'fetchChildren');
    component.toggleSubItem(item, mockEvent);
    expect(component.fetchChildren).toHaveBeenCalledWith(item);
    expect(item.open).toBe(false);

    item.open = true;
    component.toggleSubItem(item, mockEvent);
    expect(item.open).toBe(false);
  });

  it('should emit onSearchItemSelection with correct value', () => {
    spyOn(component.onSearchItemSelection, 'emit');
    const mockSearchOpt: any = { id: 1, name: 'Test Item' };

    component.searchItemClicked(mockSearchOpt);

    expect(component.onSearchItemSelection.emit).toHaveBeenCalledWith(mockSearchOpt);
  });

  it('should emit event if allowMultiSelect is true', () => {
    component.allowMultiSelect = true;
    spyOn(component.onSelectTreeItem, 'emit');

    const mockEvent = new Event('click');
    const mockItem = { folderId: '123' };

    component.selectTreeItem(mockEvent, mockItem);

    expect(component.onSelectTreeItem.emit).toHaveBeenCalledWith({
      event: mockEvent,
      item: mockItem,
    });
  });

  it('should return early and not perform action -> selectTreeItem()', ()=> {
    spyOn(component.onSelectTreeItem, 'emit');
    const mockEvent = new Event('click');
    let mockItem = { folderId: '123', disabled: false };
    
    // if readOnly is true
    component.readOnly = true;
    component.selectTreeItem(mockEvent, mockItem);
    expect(component.onSelectTreeItem.emit).not.toHaveBeenCalled();

    // if readOnly is false and allowMultiSelect is true 
    component.readOnly = false;
    component.showCheckbox = true;
    mockItem.disabled = true;
    component.allowMultiSelect = true;
    component.selectTreeItem(mockEvent, mockItem);
    expect(component.onSelectTreeItem.emit).not.toHaveBeenCalled();
  });

  it('should call stopPropagation when toggleSubItem called', () => {
    const mockEvent = jasmine.createSpyObj('event', ['stopPropagation']);

    component.toggleSubItem({}, mockEvent);

    expect(mockEvent.stopPropagation).toHaveBeenCalled();
  });

  it('should render rightSideText when provided in treeVisibleData', () => {
    component.treeVisibleData = [
      {open:true, folderId: '1', title: 'Node 1', isWorkspace:'1', rightSideText: 'Mapped' ,isActive:true, childfolderTreeVOList :[{ folderId: '1', title: 'Node 1', rightSideText: 'Mapped' ,isActive:true}] }
    ];
    fixture.detectChanges();
    const rightSideText = fixture.nativeElement.querySelector('.status-text');
    expect(rightSideText).toBeTruthy();
    expect(rightSideText.textContent).toContain('Mapped');
  });
  
  it('should render search bar when showSearchBar is true', () => {
    component.showSearchBar = true;
    fixture.detectChanges();
    const searchBar = fixture.nativeElement.querySelector('lib-search-bar, app-search-bar, search-bar');
    expect(searchBar).toBeTruthy();
  });
  
  it('should not render search bar when showSearchBar is false', () => {
    component.showSearchBar = false;
    fixture.detectChanges();
    const searchBar = fixture.nativeElement.querySelector('lib-search-bar, app-search-bar, search-bar');
    expect(searchBar).toBeFalsy();
  });
  
  it('should emit onSelectTreeItem if allowValueEmitOnTreeSelection is true and item is not disabled', () => {
    component.allowValueEmitOnTreeSelection = true;
    const mockEvent = new Event('click');
    const mockItem = { folderId: '1', disabled: false };
    spyOn(component.onSelectTreeItem, 'emit');
    component.selectTreeItem(mockEvent, mockItem);
    expect(component.onSelectTreeItem.emit).toHaveBeenCalledWith({ event: mockEvent, item: mockItem });
  });
  
  it('should not emit onSelectTreeItem if allowValueEmitOnTreeSelection is true but item is disabled', () => {
    component.allowValueEmitOnTreeSelection = true;
    const mockEvent = new Event('click');
    const mockItem = { folderId: '1', disabled: true };
    spyOn(component.onSelectTreeItem, 'emit');
    component.selectTreeItem(mockEvent, mockItem);
    expect(component.onSelectTreeItem.emit).not.toHaveBeenCalled();
  });
  
  it('should not emit onSelectTreeItem if allowValueEmitOnTreeSelection is false', () => {
    component.allowValueEmitOnTreeSelection = false;
    const mockEvent = new Event('click');
    const mockItem = { folderId: '1', disabled: false };
    spyOn(component.onSelectTreeItem, 'emit');
    component.selectTreeItem(mockEvent, mockItem);
    expect(component.onSelectTreeItem.emit).not.toHaveBeenCalled();
  });
});
