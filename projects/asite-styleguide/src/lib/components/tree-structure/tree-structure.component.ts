import { CommonModule } from '@angular/common';
import { Component, Input, Output, EventEmitter, } from '@angular/core';
import { CheckboxComponent, SearchBarComponent } from '../index';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TreeLoader } from './tree-structure.model';

@Component({
  selector: 'lib-tree-structure',
  standalone: true,
  imports: [CommonModule, SearchBarComponent, MatProgressSpinnerModule, CheckboxComponent],
  templateUrl: './tree-structure.component.html',
  styleUrl: './tree-structure.component.scss'
})

export class TreeStructureComponent {
  /**
   * @description emits to get tree child Data
   * @type {Array}
   */
  @Output() treeChildData = new EventEmitter<Event>();

  /**
  * @description emits to Search Item
  * @type {string}
  */
  @Output() searchItem = new EventEmitter<Event>();

  /**
   * @description Emit when search result item clicked
   * @memberof TreeStructureComponent
   */
  @Output() onSearchItemSelection = new EventEmitter<Event>();

  /**
   * @description Emit while tree item selection
   * @memberof TreeStructureComponent
   */
  @Output() onSelectTreeItem = new EventEmitter<{ event: Event, item: any }>();

  /**
   * @description Visible Tree Node Data
   * @type {Array}
   */
  @Input() treeVisibleData = [];

  /**
   * @description Show folder Count
   * @type {boolean}
   */
  @Input() showCount: boolean = false;

  /**
   * @description Show folder Icon
   * @type {boolean}
   */
  @Input() showFolderIcon: boolean = true;
  
  /**
   * @description Show searchbar
   * @type {boolean}
   */
  @Input() showSearchBar: boolean = true;

  /**
   * @description Show expandable Icon
   * @type {boolean}
   */
  @Input() expandableIcon: boolean = false;

  /**
   * @description Fetch tree data from remote
   * @type {boolean}
   * @memberof TreeStructureComponent
   */
  @Input() isFetchFromRemote: boolean = false;

  /**
   * @description Holds readOnly state of tree
   * @type {boolean}
   * @memberof TreeStructureComponent
   */
  @Input() readOnly: boolean = false;

  /**
   * @description Holds config data to show loader while tree loading
   * @type {boolean}
   * @memberof TreeStructureComponent
   */
  @Input() treeLoaderConfig: TreeLoader = {
    isLoading: false,
    diameter: '30'
  };

  /**
   * @description Method to search folder data from server
   * @type {Function}
   * @memberof TreeStructureComponent
   */
  @Input() searchFolderItems: Function;

  /**
   * @description Boolean to check multiselect folder enable or not
   * @type {boolean}
   * @memberof TreeStructureComponent
   */
  @Input() allowMultiSelect: boolean = false;

  /**
   * @description Boolean to allow emit the select tree item value
   * @type {boolean}
   * @memberof TreeStructureComponent
   */
  @Input() allowValueEmitOnTreeSelection: boolean = false;

  /**
   * @description Boolean to display checkbox with all sub-folder
   * @type {boolean}
   * @memberof TreeStructureComponent
   */
  @Input() showCheckbox: boolean = false;

  /**
   * @description set current selected folder details
   * @private
   * @memberof TreeStructureComponent
   */
  protected currentSelected = undefined;

  /**
   * @description set current selected folder ID
   * @public
   * @memberof TreeStructureComponent
   */
  public selectedFolderId = "";

  /**
   * @description Select Tree Item
   * @param {*} e
   * @param {*} item
   * @param {boolean} isContextEvent
   * @memberof TreeStructureComponent
   */
  selectTreeItem(e, item, isContextEvent?: boolean) {
    if(this.readOnly) {
      return;
    }

    if(this.allowValueEmitOnTreeSelection && !item.disabled){
      this.onSelectTreeItem.emit({ event: e, item: item });
    }

    if(this.allowMultiSelect) {
      if(this.showCheckbox && (item.disabled || this.readOnly)) {
        return;
      }
      item.selected = !item.selected;
      this.onSelectTreeItem.emit({ event: e, item: item });
      return;
    }
    this.selectedFolderId = item.folderId;
    if (
      (item.isTemplate && !item.isAdminAccess || !item.disabled) ||
      (isContextEvent && item.selected)
    ) {
      return;
    }
    if (this.currentSelected) {
      this.currentSelected.selected = false;
    }
    this.currentSelected = item;
  }

  /**
   * @description toggle sub children
   * @param {*} item
   * @memberof TreeStructureComponent
   */
  toggleSubItem(item: any, event: Event): void {
    event.stopPropagation();
    if (!item.open) {
      this.fetchChildren(item);
      return;
    }
    item.open = !item.open;
  }

  /**
   * @description fetch children data
   * @param {*} item
   * @memberof TreeStructureComponent
   */
  fetchChildren(item) {
    item.open = true;
    if(this.isFetchFromRemote) {
      item.isFetching = true;
    }
    this.treeChildData.emit(item);
  }

  /**
   * @description search selected folder
   * @param {*} item
   * @memberof TreeStructureComponent
   */
  searchFolders(item) {
    this.searchItem.emit(item);
  }

  /**
   * @description Trigger on search result item clicked
   * @param {*} searchOpt
   * @memberof TreeStructureComponent
   */
  searchItemClicked(searchOpt) {
    this.onSearchItemSelection.emit(searchOpt);
  }
}
