import { CommonModule } from '@angular/common';
import { Component, ContentChild, TemplateRef } from '@angular/core';

@Component({
  selector: 'lib-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  standalone: true,
  imports: [
    CommonModule
  ],
})
export class HeaderComponent {
  @ContentChild('leftTemplate') leftTemplate: TemplateRef<any>;
  // @ContentChild('leftTemplate') leftTemplate: HeaderComponent;

  @ContentChild('centerTemplate') centerTemplate: TemplateRef<any>;
  // @ContentChild('centerTemplate') centerTemplate: HeaderComponent;

  @ContentChild('rightTemplate') rightTemplate: TemplateRef<any>;
  // @ContentChild('rightTemplate') rightTemplate: HeaderComponent;
  // @ContentChild('rightTemplate', { read: TemplateRef }) rightTemplate!: TemplateRef<any>;
}
