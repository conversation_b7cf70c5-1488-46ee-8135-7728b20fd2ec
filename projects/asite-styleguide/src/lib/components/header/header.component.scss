@import '../../../../assets/scss/styles.scss';

#global-header {
  font-size: 1.25rem;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.wrapper {
  color: var(--lib-color-white, #ffffff);
  background-color: var(--lib-color-primary, #4940d7);
  font-family: $fontFamily;
  width: auto;
  height: 46px; 
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  padding: 0px 16px;
}

.left-content {
  align-items: center;
  display: flex;
  padding: 4px;
  gap: 2px;
  flex: 1 0 0;
  align-self: stretch;
}

.center-content {
  display: flex;
  padding: 6px 12px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 9px;
  flex: 1 0 0;
  align-self: stretch;
  color: var(--lib-color-white, #ffffff);
}

.right-content {
  display: flex;
  padding: 4px;
  align-items: flex-end;
  gap: 10px;
  justify-content: flex-end;
  flex: 1 0 0;
}