import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HeaderComponent } from './header.component';
import { Component, TemplateRef, ViewChild } from '@angular/core';

describe('HeaderComponent', () => {
  let component: HeaderComponent;
  let fixture: ComponentFixture<HeaderComponent>;

  beforeEach(async () => {
    fixture = TestBed.createComponent(HeaderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

describe('Without TemplateRef', () => {
  let component: HeaderComponent;
  let fixture: ComponentFixture<HeaderComponent>;
  beforeEach(async () => {
    fixture = TestBed.createComponent(HeaderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('does not set template references', () => {
    const leftTemplateContent = component.leftTemplate;
    const centerTemplateContent = component.centerTemplate;
    const rightTemplateContent = component.rightTemplate;

    expect(leftTemplateContent).toBeUndefined();
    expect(centerTemplateContent).toBeUndefined();
    expect(rightTemplateContent).toBeUndefined();
  });
});

@Component({
  template: `
    <lib-header>
      <ng-template #leftTemplate>Left Content</ng-template>
      <ng-template #centerTemplate>Center Content</ng-template>
      <ng-template #rightTemplate>Right Content</ng-template>
    </lib-header>
  `,
})
class TestComponent
{
  @ViewChild('leftTemplate') leftTemplate!: TemplateRef<any>;
  @ViewChild('centerTemplate') centerTemplate!: TemplateRef<any>;
  @ViewChild('rightTemplate') rightTemplate!: TemplateRef<any>;
}

describe('TemplateRef', () => {
  let component: HeaderComponent;
  let fixture: ComponentFixture<TestComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TestComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TestComponent);
    component = fixture.debugElement.children[0].componentInstance as any;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set template references', () => {
    const haveLeftTemplateRef: TemplateRef<any> = component.leftTemplate;
    const haveCenterTemplateRef: TemplateRef<any> = component.centerTemplate;
    const haveRightTemplateRef: TemplateRef<any> = component.rightTemplate;

    expect(haveLeftTemplateRef).toBeTruthy();
    expect(haveCenterTemplateRef).toBeTruthy();
    expect(haveRightTemplateRef).toBeTruthy();
  });
});
