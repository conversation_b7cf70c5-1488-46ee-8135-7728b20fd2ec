<div class="checkbox-wrapper">
  <p *ngIf="label">{{label}} <span [ngClass]="{'aestric':isRequired}" *ngIf="isRequired || false">&#42;</span></p>
  <div class="input-wrapper" [ngClass]="titleLabelPosition">
    <ng-container *ngIf="titleLabelPosition === 'left'">
      <i [class]="icon" [ngClass]="{'disable': disabled}" aria-hidden="true" *ngIf="icon" role="img"></i>
      <label [ngClass]="{'disable': disabled}" [for]="uid" *ngIf="title">{{title}}</label>
    </ng-container>
    <input type="checkbox" 
    type="checkbox"
    [id]="uid" 
    [name]="name" 
    [required]="isRequired" 
    [disabled]="disabled"
    (click)="sendEvent($event)"
    [checked]="checked"
    [indeterminate]="indeterminate"
    tabindex="0"
    (ngModelChange)="onModelChange($event)"
    >
    <ng-container *ngIf="titleLabelPosition === 'right'">
      <i [class]="icon" [ngClass]="{'disable': disabled}" aria-hidden="true" *ngIf="icon" role="img"></i>
      <label [ngClass]="{'disable': disabled}" [for]="uid" *ngIf="title">{{title}}</label>
    </ng-container>
  </div>
</div>