import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CommonModule } from '@angular/common';
import { CheckboxComponent } from './checkbox.component';

describe('CheckboxComponent', () => {
  let component: CheckboxComponent;
  let fixture: ComponentFixture<CheckboxComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CommonModule]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CheckboxComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit checked value on change', () => {
    spyOn(component.checkedChange, 'emit');
    const mockEvent = { target: { checked: true } };
    component.onModelChange(true);
    expect(component.checkedChange.emit).toHaveBeenCalledWith(true);
  });

  // it('should update checked value on writeValue', () => {
  //   const checkedValue = true;
  //   component.writeValue(checkedValue);
  //   expect(component.checked).toEqual(checkedValue);
  // });

  it('should update checked value on onModelChange', () => {
    const checkedValue = true;
    component.onModelChange(checkedValue);
    expect(component.checked).toEqual(checkedValue);
  });

  it('should register on change', () => {
    const mockFn = jasmine.createSpy('mockFn');
    component.registerOnChange(mockFn);
    component.onChange(true);
    expect(mockFn).toHaveBeenCalledWith(true);
  });

  it('should register on touched', () => {
    const mockFn = jasmine.createSpy('mockFn');
    component.registerOnTouched(mockFn);
    component.onTouch();
    expect(mockFn).toHaveBeenCalled();
  });

  it("should emit on checkbox change",()=>{
    spyOn(component.checkedChange,'emit');
    let event = {target:{checked:true}};
    component.sendEvent(event);
    expect(component.checkedChange.emit).toHaveBeenCalledWith(event.target.checked);
  })
});