import { CommonModule } from "@angular/common";
import { Component, EventEmitter, Input, OnInit, Output, forwardRef } from "@angular/core";
import {
  ControlValueAccessor,
  FormControl,
  FormsModule,
  NG_VALUE_ACCESSOR
} from "@angular/forms";
import { CheckboxLabelPosition } from "./checkbox.model";

@Component({
  selector: "lib-checkbox",
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './checkbox.component.html',
  styleUrls: ["./checkbox.component.scss"],

  //  copy paste this providers property
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CheckboxComponent),
      multi: true
    }
  ]
})

export class CheckboxComponent implements OnInit {

  /**
   * Checkbox contents
   *
   * @required
   */
  @Input() label: string = '';

  @Input() uid: string = '';

  @Input() title: string = '';

  /**
   * Set to `true` for a disabled checkbox.
   */
  @Input() disabled: boolean = false;

  /**
   * Set to `true` for a checked checkbox.
   */

  /**
   * Reflects the required attribute of the `input` element.
   */
  @Input() isRequired: boolean = false;

  @Input() icon: string = '';

  /**
   * Sets the name attribute on the `input` element.
   */
  @Input() name: string = '';
 
  
  /**
   * Sets the indeterminate attribute on the `input` element.
   */
  @Input() indeterminate: boolean = false;

  /**
   * @description Set position of title label
   * @type {string}
   * @memberof CheckboxComponent
   */
  @Input() titleLabelPosition: string = CheckboxLabelPosition.Right;
 
    /**
   * Emits an event when the value of the checkbox changes.
   */
  @Output() checkedChange = new EventEmitter<any>();

  //  Copy paste this stuff here
  onChange: any = () => {};
  onTouch: any = () => {};

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouch = fn;
  }

  constructor() {}

  ngOnInit() {}

  //  Define what should happen in this component, if something changes outside
  @Input() checked: boolean = false;
  // writeValue(checked: boolean) {
  //   this.checked = checked;
  // }

  sendEvent(event){
    this.checked = event.target.checked;
    this.checkedChange.emit(event.target.checked);
  }

  onModelChange(e: boolean) {
    //  bind the changes to the local value
    this.checked = e;

    //  Handle what should happen on the outside, if something changes on the inside
    this.onChange(e);
    this.checkedChange.emit(e);
  }
}
