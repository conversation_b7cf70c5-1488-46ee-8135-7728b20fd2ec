@import "../../../../assets/scss/styles.scss";

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: var(--lib-color-black, #0C1315);
    opacity: 20%;
}

#side-panel-wrapper {
    font-family: $fontFamily;
    height: 100vh;
    overflow: auto;

    .panel {
        position: fixed;
        top: 0;
        right: 0;
        height: 100vh;
        background: var(--lib-color-white, #ffffff);
        box-shadow: none;
    }
  
    .side-panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid var(--lib-color-gray-6,#E9E9E9);
        padding: 15px;
        overflow: auto;
    
        .side-panel-title {
            text-overflow: ellipsis;
            overflow: hidden;
        }
    
        .side-panel-header-actions {
            display: flex;
            gap: 10px;
            justify-content: space-between;
        }
    }
  
    .side-panel-body {
        overflow: auto;
        height: calc(100% - 73px);
        padding: 15px;
    }
}