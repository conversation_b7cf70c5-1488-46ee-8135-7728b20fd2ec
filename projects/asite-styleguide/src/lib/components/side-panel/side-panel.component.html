<!-- Backdrop element when side panel is opened -->
<div class="backdrop" *ngIf="opened" (click)="closeSidePanel()"></div>

<div id="side-panel-wrapper">
    <mat-sidenav-container>
        <mat-sidenav 
            #sidenav 
            mode="over" 
            class="panel" 
            (closedStart)="closeSidePanel($event)"
            [opened]="opened"
            [fixedInViewport]="true" 
            [fixedTopGap]="0" 
            [fixedBottomGap]="0" 
            [position]="'end'"
            [ngStyle]="isPanelExpanded ? { width: customResizeWidth } : { width: customWidth }">
            <div class="side-panel-header">
                <h3 class="side-panel-title">
                    {{title}}
                </h3>
        
                <div class="side-panel-header-actions">
                    <div class="side-panel-actions">
                        <!-- Actions content of side panel via ng-content -->
                        <ng-content select=".side-panel-actions"></ng-content>
                    </div>
            
                    <lib-button
                        [iconLeft]="isPanelExpanded ? 'iconoir-collapse' : 'iconoir-expand'" 
                        [buttonType]="buttonType.Secondary" 
                        [size]="size.Medium"
                        class="resize-button"
                        (click)="resizeSidePanel($event)">
                    </lib-button>
            
                    <lib-button
                        [iconLeft]="'iconoir-xmark'" 
                        [buttonType]="buttonType.Tertiary" 
                        [size]="size.Medium"
                        class="close-button"
                        (click)="closeSidePanel($event)">
                    </lib-button>
                </div>
            </div>
            <div class="side-panel-body">
                <!-- Body content of side panel via ng-content -->
                <ng-content select=".side-panel-content"></ng-content>
            </div>
        </mat-sidenav>
    </mat-sidenav-container>
</div> 