import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SidePanelComponent } from './side-panel.component';
import { ButtonComponent } from '../button/button.component';
import { MatSidenavModule } from '@angular/material/sidenav';
import { By } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

describe('SidePanelComponent', () => {
  let component: SidePanelComponent;
  let fixture: ComponentFixture<SidePanelComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SidePanelComponent, MatSidenavModule, ButtonComponent, BrowserAnimationsModule],
    }).compileComponents();

    fixture = TestBed.createComponent(SidePanelComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should use default title', () => {
    expect(component.title).toBe('Title');
  });

  it('should update title via input', () => {
    component.title = 'My Panel';
    fixture.detectChanges();

    const titleElement = fixture.nativeElement.querySelector('.side-panel-title');
    expect(titleElement.textContent.trim()).toBe('My Panel');
  });

  it('should expand panel and emit event on click of resize button', () => {
    const event = new Event('click');
    component.isPanelExpanded = false;

    spyOn(component.onResizeSidePanel, 'emit');

    const resizeElement = fixture.debugElement.query(By.css('.resize-button'));
    resizeElement.nativeElement.dispatchEvent(event);
    fixture.detectChanges();

    expect(component.isPanelExpanded).toBeTrue();
    expect(component.onResizeSidePanel.emit).toHaveBeenCalledWith(event);
  });

  it('should collapse panel and emit event on click of resize button', () => {
    const event = new Event('click');
    component.isPanelExpanded = true;

    spyOn(component.onResizeSidePanel, 'emit');

    const resizeButtonElement = fixture.debugElement.query(By.css('.resize-button'));
    resizeButtonElement.nativeElement.dispatchEvent(event);
    fixture.detectChanges();

    expect(component.isPanelExpanded).toBeFalse();
    expect(component.onResizeSidePanel.emit).toHaveBeenCalledWith(event);
  });

  it('should close panel and emit close event on click of close button', () => {
    const event = new Event('click');
    component.opened = true;

    spyOn(component.onCloseSidePanel, 'emit');

    const closeButtonElement = fixture.debugElement.query(By.css('.close-button'));
    closeButtonElement.nativeElement.dispatchEvent(event);
    fixture.detectChanges();

    expect(component.isPanelExpanded).toBeFalse();
    expect(component.opened).toBeFalse();
    expect(component.onCloseSidePanel.emit).toHaveBeenCalledWith(event);
  });

  it('should apply width when side panel is not expanded', () => {
    component.opened = true;
    component.isPanelExpanded = false;
    component.customWidth = '40%';

    const panelElement = fixture.debugElement.query(By.css('.panel')).nativeElement;
    expect(panelElement.style.width).toBe('40%');
  });

  it('should change width of side panel when expanded', () => {
    component.opened = true;
    component.isPanelExpanded = false;
    component.customResizeWidth = '90%';
    fixture.detectChanges();

    const resizeButtonElement = fixture.debugElement.query(By.css('.resize-button'));
    resizeButtonElement.nativeElement.dispatchEvent(new Event('click'));
    fixture.detectChanges();

    const panelElement = fixture.debugElement.query(By.css('.panel')).nativeElement;
    expect(panelElement.style.width).toBe('90%');
  });

  it('should show backdrop when panel is open', () => {
    component.opened = true;
    fixture.detectChanges();

    const backdropElement = fixture.debugElement.query(By.css('.backdrop'));
    expect(backdropElement).toBeTruthy();
  });

  it('should hide backdrop on click of close button', () => {
    const event = new Event('click');
    component.opened = true;

    const closeButtonElement = fixture.debugElement.query(By.css('.close-button'));
    closeButtonElement.nativeElement.dispatchEvent(event);
    fixture.detectChanges();

    expect(component.opened).toBeFalse();
    const backdropElement = fixture.debugElement.query(By.css('.backdrop'));
    expect(backdropElement).toBe(null);
  });

  it('should close panel on click of backdrop', () => {
    component.opened = true;
    fixture.detectChanges();

    const backdropElement = fixture.debugElement.query(By.css('.backdrop'));
    backdropElement.nativeElement.dispatchEvent(new Event('click'));
    expect(component.opened).toBeFalse();
  });
});
