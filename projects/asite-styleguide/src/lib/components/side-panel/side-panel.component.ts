import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from "../button/button.component";
import { MatSidenavModule } from '@angular/material/sidenav';
import { ButtonSizeEnum, ButtonTypeEnum } from '../button/button.model';
import { SearchBarComponent } from '../search-bar/search-bar.component';

@Component({
  selector: 'lib-side-panel',
  standalone: true,
  imports: [CommonModule, ButtonComponent, MatSidenavModule, SearchBarComponent],
  templateUrl: './side-panel.component.html',
  styleUrl: './side-panel.component.scss'
})
export class SidePanelComponent {

  /**
   * @description Set the title of side panel
   * @type {string}
   * @memberof SidePanelComponent
   */
  @Input('title') title: string = 'Title';

  /**
   * @description Custom width of side panel
   * @type {string}
   * @memberof SidePanelComponent
   */
  @Input('customWidth') customWidth: string = '40%';

  /**
   * @description Custom resize width of side panel
   * @type {string}
   * @memberof SidePanelComponent
   */
  @Input('customResizeWidth') customResizeWidth: string = '70%';

  /**
   * @description Side panel opened or not
   * @type {boolean}
   * @memberof SidePanelComponent
   */
  @Input('opened') opened: boolean = false;
  
  /**
   * @description Side panel is expanded or not
   * @type {Boolean}
   * @memberof SidePanelComponent
   */
  public isPanelExpanded: boolean = false;
  
  /**
   * @description Asite style-guide button configuration
   * @type {ButtonTypeEnum}
   * @memberof SidePanelComponent
   */
  public buttonType = ButtonTypeEnum;

  /**
   * @description Asite style-guide button size configuration
   * @type {ButtonSizeEnum}
   * @memberof SidePanelComponent
   */
  public size = ButtonSizeEnum;

  /**
   * @description Event emitted when side panel is closed
   * @memberof SidePanelComponent
   */
  @Output() onCloseSidePanel = new EventEmitter<Event>();

  /**
   * @description Event emitted when side panel is resized
   * @memberof SidePanelComponent
   */
  @Output() onResizeSidePanel = new EventEmitter<Event>();

  constructor() {}

  /**
   * @description Resize side panel
   * @memberof SidePanelComponent
   */
  resizeSidePanel(e: Event) {
    this.isPanelExpanded = !this.isPanelExpanded;
    this.onResizeSidePanel.emit(e);
  }

  /**
   * @description Close side panel
   * @memberof SidePanelComponent
   */
  closeSidePanel(e: Event) {
    this.isPanelExpanded = false;
    this.opened = false;
    this.onCloseSidePanel.emit(e);
  }
}
