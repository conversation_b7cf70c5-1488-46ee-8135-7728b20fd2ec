import { ComponentFixture, TestBed } from '@angular/core/testing';
import { StepperComponent } from './stepper.component';

describe('StepperComponent', () => {
  let component: StepperComponent;
  let fixture: ComponentFixture<StepperComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [StepperComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(StepperComponent);
    component = fixture.componentInstance;
    // Mock data for steps
    component.steps = [
      { id: 1, label: 'Step 1', status: 'Current' },
      { id: 2, label: 'Step 2', status: 'Unvisited' },
      { id: 3, label: 'Step 3', status: 'Unvisited' },
      { id: 4, label: 'Step 4', status: 'Error' },
      { id: 5, label: 'Step 5', status: 'Disabled' }
    ];
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should update the current step and call updateStepStatus if the clicked step is not disabled', () => {
    spyOn(component, 'updateStepStatus');

    component.stepClicked(1); // 'Unvisited' step
    expect(component.updateStepStatus).toHaveBeenCalledWith(1);
    expect(component.currentStep).toBe(1);

    component.stepClicked(3); // 'Error' step
    expect(component.updateStepStatus).toHaveBeenCalledWith(3);
    expect(component.currentStep).toBe(3);
  });

  it('should not update the current step or call updateStepStatus if the clicked step is disabled', () => {
    spyOn(component, 'updateStepStatus');

    component.stepClicked(4); // 'Disabled' step
    expect(component.updateStepStatus).not.toHaveBeenCalled();
    expect(component.currentStep).not.toBe(4);
  });

  it('should update the step statuses correctly when a step is clicked', () => {
    component.stepClicked(2); // Click 'Step 3'

    expect(component.steps[0].status).toBe('Visited');
    expect(component.steps[1].status).toBe('Visited');
    expect(component.steps[2].status).toBe('Current');
    expect(component.steps[3].status).toBe('Error');
    expect(component.steps[4].status).toBe('Disabled');
  });

  it('should update the step statuses correctly when a step is clicked which is error status', () => {
    component.stepClicked(3); // Click 'Step 4'

    expect(component.steps[0].status).toBe('Visited');
    expect(component.steps[1].status).toBe('Visited');
    expect(component.steps[2].status).toBe('Visited');
    expect(component.steps[3].status).toBe('Current');
    expect(component.steps[4].status).toBe('Disabled');
  });

  it('should update the step statuses correctly when a step is clicked after visited error step', () => {
    component.stepClicked(3); // Click 'Step 4'
    component.stepClicked(1); // Click 'Step 2'

    expect(component.steps[0].status).toBe('Visited');
    expect(component.steps[1].status).toBe('Current');
    expect(component.steps[2].status).toBe('Unvisited');
    expect(component.steps[3].status).toBe('Unvisited');
    expect(component.steps[4].status).toBe('Disabled');
  });

  it('should get the correct icon for each step status', () => {
    expect(component.getStepIcon('Error')).toBe('iconoir-warning-circle');
    expect(component.getStepIcon('Visited')).toBe('iconoir-check-circle');
    expect(component.getStepIcon('Current')).toBe('');
    expect(component.getStepIcon('Disabled')).toBe('iconoir-circle disabled');
    expect(component.getStepIcon('Unvisited')).toBe('iconoir-circle');
  });

  it('should get the correct connector class for each step status', () => {
    expect(component.getConnectorClass('Error')).toBe('stepper-connector error');
    expect(component.getConnectorClass('Visited')).toBe('stepper-connector visited');
    expect(component.getConnectorClass('Current')).toBe('stepper-connector current');
    expect(component.getConnectorClass('Disabled')).toBe('stepper-connector disabled');
    expect(component.getConnectorClass('Unvisited')).toBe('stepper-connector unvisited');
  });
});
