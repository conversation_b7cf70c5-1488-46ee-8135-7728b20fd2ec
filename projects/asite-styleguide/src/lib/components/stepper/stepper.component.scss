@import "../../../../assets/scss/styles.scss";

.lib-stepper {
    font-family: $fontFamily;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;

    .lib-stepper-title {
        font-size: 24px;
        text-align: center;
        padding: 20px;
    }

    .lib-stepper-main {
        .steps-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 0px 50px;

            .stepper-icons {
                display: flex;
                font-size: 20px;
                align-items: center;
                cursor: pointer;

                .radio-icon {
                    padding: 8px;

                    .circle {
                        width: 20px;
                        height: 20px;
                        position: relative;
                        border-radius: 50%;
                        border: 1.6px solid var(--lib-color-primary, #4940D7);

                        .dot {
                            width: 10px;
                            height: 10px;
                            border-radius: 50%;
                            background-color: var(--lib-color-primary, #4940D7);
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                        }

                    }

                    &:hover {
                        border-radius: 40px;
                        background: var(--lib-color-secondary, #ECEBFC);
                    }
                }
            }

            .stepper-connector {
                width: 100%;
                height: 2px;
                border-radius: 4px;
                background: var(--lib-color-secondary, #ECEBFC);

                &.visited {
                    background: var(--lib-color-primary, #4940D7);
                }
            }

            .disabled {
                color: var(--lib-color-gray-4, #8D8D8D);
                cursor: not-allowed;

                &:hover {
                    border-radius: 40px;
                    background: var(--lib-color-gray-6, #E9E9E9);
                }
            }

            .iconoir-warning-circle {
                color: #E43B2C;

                &:hover {
                    border-radius: 40px;
                    background: var(--lib-color-red-8, #FFF0ED);
                }
            }

            i {
                &:hover {
                    border-radius: 40px;
                    background: var(--lib-color-secondary, #ECEBFC);
                }
            }
        }

        .stepper-label-container {
            display: flex;
            justify-content: space-between;
            text-align: center;

            .stepper-container {
                width: 145px;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }

    .bottom-dot-stepper {
        display: flex;
        justify-content: center;
        gap: 16px;

        .stepper-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            cursor: pointer;
            background: var(--lib-color-primary, #4940D7);
        }

    }

    i {
        padding: 8px;
        font-size: 23px;
        color: var(--lib-color-primary, #4940D7);
    }

    .hover {
        border-radius: 40px;
        background: var(--lib-color-secondary, #ECEBFC);
    }

    .lib-stepper-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
}