import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, ContentChild, Input, Output, EventEmitter, TemplateRef } from '@angular/core';
import { StepperStep } from '../index';

@Component({
  selector: 'lib-stepper',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './stepper.component.html',
  styleUrl: './stepper.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class StepperComponent {

  /**
   * @description Template reference for the stepper title.
   */
  @ContentChild('stepperTitle') stepperTitle: TemplateRef<any> | null = null;

  /**
   * @description Template reference for the stepper container.
   */
  @ContentChild('steppeContainer') steppeContainer: TemplateRef<any> | null = null;

  /**
   * @description Template reference for the footer content.
   */
  @ContentChild('footerContent') footerContent: TemplateRef<any> | null = null;

  /**
   * @description Array containing steps for the stepper component.
   * @memberof StepperComponent
   * Each step includes an ID, label, and status.
   */
  @Input() steps: StepperStep[] = [];

  /**
   * @description Index of the current step. This property allows the parent component to set
   * the current step from outside the stepper component. It represents the index of the step
   * that is currently active/selected.
   * 
   * @type {number}
   */
  @Input() currentStep: number = 0;

  /**
   * @description Event emitter to notify the parent component of changes to the current step.
   * Whenever the current step changes (e.g., when a user clicks on a different step), this
   * event is emitted with the new current step index. This allows two-way data binding with the
   * parent component, ensuring that changes in the stepper component are reflected in the parent.
   * 
   * @type {EventEmitter<number>}
   */
  @Output() currentStepChange = new EventEmitter<number>();

  constructor() { }

  /**
   * @description Handles click event on a step.
   * @param index - Index of the step clicked.
   */
  stepClicked(index: number): void {
    if (this.steps[index].status !== 'Disabled') {
      this.updateStepStatus(index);
      this.currentStep = index;
      this.currentStepChange.emit(this.currentStep);
    }
  }

  /**
   * @description Updates the status of steps based on the clicked index.
   * @param index - Index of the clicked step.
   */
  updateStepStatus(index: number): void {
    for (let i = 0; i < this.steps.length; i++) {
      if (i < index) {
        this.steps[i].status = 'Visited';
      } else if (i === index) {
        this.steps[i].status = 'Current';
      } else if (this.steps[i].status !== 'Error' && this.steps[i].status !== 'Disabled') {
        this.steps[i].status = 'Unvisited';
      }
    }
  }

  /**
   * @description Gets the appropriate icon based on the status of a step.
   * @param status - Status of the step.
   * @returns Icon class name.
   */
  getStepIcon(status: string): string {
    switch (status) {
      case 'Error':
        return 'iconoir-warning-circle';
      case 'Visited':
        return 'iconoir-check-circle';
      case 'Current':
        return '';
      case 'Disabled':
        return 'iconoir-circle disabled';
      case 'Unvisited':
      default:
        return 'iconoir-circle';
    }
  }

  /**
   * @description Gets the CSS class for the connector based on the status of a step.
   * @param status - Status of the step.
   * @returns Connector class name.
   */
  getConnectorClass(status: string): string {
    return `stepper-connector ${status.toLowerCase()}`;
  }
}
