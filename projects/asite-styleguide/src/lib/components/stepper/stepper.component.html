<div class="lib-stepper">
    <div class="lib-stepper-title" *ngIf="stepperTitle">
        <ng-container *ngTemplateOutlet="stepperTitle"></ng-container>
    </div>
    <div class="lib-stepper-main">
        <div class="steps-container">
            <ng-container *ngFor="let step of steps; let i = index; last as isLast">
                <div class="stepper-icons" (click)="stepClicked(i)">
                    <ng-container *ngIf="step.status === 'Current'; else iconTemplate">
                        <div class="radio-icon">
                            <div class="circle">
                                <div class="dot"></div>
                            </div>
                        </div>
                    </ng-container>
                    <ng-template #iconTemplate>
                        <i [class]="getStepIcon(step.status)">
                            <span class="current" *ngIf="step.status === 'Current'"></span>
                        </i>
                    </ng-template>
                </div>
                <span class="stepper-connector" *ngIf="!isLast" [ngClass]="getConnectorClass(step.status)"></span>
            </ng-container>
        </div>
        <div class="stepper-label-container">
            <ng-container *ngFor="let step of steps; let i = index">
                <div class="stepper-container">
                    <div class="stepper-label">
                        <p *ngIf="step.label">{{ step.label }}</p>
                    </div>
                    <p *ngIf="step.sublabel">{{ step.sublabel }}</p>
                </div>
            </ng-container>
        </div>
    </div>

    <div class="lib-stepper-body" *ngIf="steppeContainer">
        <ng-container *ngTemplateOutlet="steppeContainer"></ng-container>
    </div>

    <!-- commented as not required currently -->
    <!-- <div class="bottom-dot-stepper">
        <ng-container *ngFor="let step of steps; let i = index; last as isLast">
            <span class="stepper-dot" (click)="stepClicked(i)" [ngClass]="getDotClass(step.status)"></span>
        </ng-container>
    </div> -->

    <div class="lib-stepper-footer" *ngIf="footerContent">
        <ng-container *ngTemplateOutlet="footerContent"></ng-container>
    </div>
</div>