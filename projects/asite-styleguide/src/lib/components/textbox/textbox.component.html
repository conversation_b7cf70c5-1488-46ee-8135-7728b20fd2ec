<div class="textbox-wrapper" [ngClass]="inputSettings.inputState">
    <label class="textbox-label"  [ngClass]="{'unobserved': !isShowLabel || !inputSettings.label}" for="{{inputSettings.id || inputSettings.label}}" [attr.id]="'label-' + inputSettings.id">{{inputSettings.label}} <span class="field-error" *ngIf="inputSettings.isRequired || false">&#42;</span></label>
    <div class="input-wrapper">
        <input *ngIf="!inputSettings.multiline" [maxlength]="maxLength" [type]="inputSettings.type || 'text'" class="textbox-input"
            [placeholder]="inputSettings.placeholder || ''" [disabled]="inputSettings.disabled"
            [(ngModel)]="inputValue" (input)="emitInputValue()" [matAutocomplete]="auto"
            [matAutocompleteDisabled]="filteredOption.length === 0"
            [ngStyle]="{'padding-right': inputSettings.crossIcon && inputSettings.maskIcon ? '60px': inputSettings.crossIcon || inputSettings.maskIcon ? '32px': '16px'}" (blur)="emitBlur()"
            [attr.id]="inputSettings.id || inputSettings.label">
        <textarea *ngIf="inputSettings.multiline" [maxlength]="maxLength" [rows]="inputSettings.rows" class="textbox-textarea"
            [placeholder]="inputSettings.placeholder || 'Enter value..'" [disabled]="inputSettings.disabled"
            [(ngModel)]="inputValue" (input)="emitInputValue()" (blur)="emitBlur()" [attr.id]="inputSettings.id || inputSettings.label"></textarea>
        <div class="icon-wrapper">
            <i class="iconoir-eye" (click)="switchType()" *ngIf="inputSettings.maskIcon" title="show/hide password"></i>
            <i class="iconoir-xmark" (click)="clearInput()"
                *ngIf="inputSettings.crossIcon && ('' + inputValue)?.length && !inputSettings.disabled"
                title="Remove input"></i>
        </div>
    </div>
    <div class="helper-info" *ngIf="inputSettings.infoText">
        <p [ngClass]="{'disable': inputSettings.disabled}"> {{inputSettings.infoText}}
        </p>
        <i *ngIf="inputSettings.inputState === 'success'" class="iconoir-check-circle" title="success"></i>
        <i *ngIf="inputSettings.inputState === 'error'" class="iconoir-warning-circle" [title]="inputSettings.errorTooltip"></i>
    </div>
    <mat-autocomplete #auto="matAutocomplete" (optionSelected)="optionSelected($event)">
        <ng-container *ngFor="let item of optionList">
            <div class="option-dd">
                <mat-option *ngIf="typeheadConfig.displayKey" class="asite-mat-option"
                    [value]="item[typeheadConfig['displayKey']]"  (mousedown)=$event.preventDefault()>
                    <img *ngIf="typeheadConfig.imageKey && item[typeheadConfig['imageKey']]" #img [src]="item[typeheadConfig['imageKey']]"
                        class="asite-mat-option-img" (error)="img.src = typeheadConfig['fallbackImgUrl']"
                        alt="img-beside-list-text">
                    {{item[typeheadConfig['displayKey']]}}
                </mat-option>
            </div>
        </ng-container>
    </mat-autocomplete>
</div>
