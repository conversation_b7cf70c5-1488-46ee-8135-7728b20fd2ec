export interface InputSettings {
    type?: string,
    disabled?: boolean,
    inputState?: string,
    label?: string,
    infoText?: string,
    crossIcon?: boolean,
    maskIcon?: boolean,
    placeholder?:string,
    width?: string,
    isRequired?:boolean,
    multiline?: boolean,
    rows?: number,
    id?: string,
    errorTooltip?: string
    supportReactiveForm?: boolean,
}

export interface TypeheadConfig {
    displayKey: string,
    imageKey?: string,
    fallbackImgUrl?: string,
}
// inputState - options are : (success, error, readOnly) it gives input border respective to its corresponding state

// maskIcon - eye icon to hide & show password field to user

//crossIcon - cross icon to clear input text

//width - to override default 300px width of textbox

//type - input type - (text,password,email)

//label - label above textbox

//infoText - label exact below textbox for some info text

//disabled - to enable/disable textbox field

//isRequired - display asterisk on top of label

//multiline - to display textarea

//rows - to define the height of textarea

//isShowLable - set visibility hidden for the label fix for the accessbility
