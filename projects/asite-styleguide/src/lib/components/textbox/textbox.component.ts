import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { InputSettings, TypeheadConfig } from './textbox.model';
import {
  ControlValueAccessor,
  FormsModule,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from '@angular/forms';
import { Obj } from '../select/select.model';
import {
  MatAutocompleteModule,
  MatAutocompleteSelectedEvent,
} from '@angular/material/autocomplete';

@Component({
  selector: 'lib-textbox',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatAutocompleteModule,
  ],
  templateUrl: './textbox.component.html',
  styleUrl: './textbox.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: TextboxComponent,
      multi: true,
    },
  ],
})
export class TextboxComponent implements ControlValueAccessor {
  /*** @description two way binding to get value of inputfield */
  @Input() inputValue = '';

  @Input() maxLength!:number;

  /*** @description event to emit inputfield value to parent */
  @Output() inputValueChange = new EventEmitter<string>();

  /*** @description emit inputfield value to parent on input Blur */
  @Output() onBlur = new EventEmitter<string>();
  @Output() itemSelected = new EventEmitter<string>();

  @Input() isShowLabel:boolean = true;

  /*** @description config object for inputfield & textarea */
  @Input() inputSettings: InputSettings = {
    type: 'text',
    disabled: false,
    inputState: 'default',
    crossIcon: true,
    maskIcon: false,
    label: '',
    infoText: '',
    width: '',
    isRequired: false,
    multiline: false,
    rows: 6,
    errorTooltip: 'error',
    supportReactiveForm: false,
  };
  /*** @description array for typeahead list to display below textbox */
  @Input() filteredOption: Array<Obj> = [];

  /*** @description find key & imageUrl from object to display */
  @Input() typeheadConfig: TypeheadConfig = {
    displayKey: '',
    imageKey: '',
    fallbackImgUrl: '',
  };

  /*** @description stores filtered array based on input value */
  optionList: Array<Obj> = [];

  //variable & function for reactive form support
  onChange = (value: string) => {};

  onTouched = () => {};

  /*** @description emits data to reactive form */
  formValue = '';

  /*** @description emits touch event to reactive form */
  touched = false;

  /**
   * @description clear input field when clicked on cross icon
   */
  clearInput() {
    this.inputValue = '';
    this.emitInputValue();
    this.emitBlur();
  }
  /**
   * @description return filtered array based on value matched
   */
  private _filter(value: string) {
    return this.filteredOption.filter((option: Obj) =>
      option[this.typeheadConfig['displayKey']]
        .toLowerCase()
        .includes(value.toLowerCase())
    );
  }
  /**
   * @description show/hide password field
   */
  switchType() {
    this.inputSettings.type =
      this.inputSettings.type === 'text' ? 'password' : 'text';
  }

  /**
   * @description updated formValue, inputVal, optionList based on Input Value
   */
  emitInputValue() {
    this.formValue = this.inputValue;
    this.inputValueChange.emit(this.inputValue);
    this.onChange(this.formValue);
    this.markAsTouched();
    this.optionList = this._filter(this.inputValue || '');
  }

  /**
   * @description emits value which gets from selected option list
   */
  optionSelected(event: MatAutocompleteSelectedEvent) {
    this.inputValue = event['option'].value;
    this.itemSelected.emit(this.inputValue);
    this.emitInputValue();
  }

  /**
   * @description to support & get value in reactive form
   */
  writeValue(value: string) {
    if(this.inputSettings?.supportReactiveForm) {
      this.inputValue = value;
    }
    this.formValue = value;
  }

  registerOnChange(onChange: any) {
    this.onChange = onChange;
  }

  registerOnTouched(onTouched: any) {
    this.onTouched = onTouched;
  }

  markAsTouched() {
    if (!this.touched) {
      this.onTouched();
      this.touched = true;
    }
  }

  /**
   * @description updates typeahead data list
   */
  ngOnChanges() {
    if(this.typeheadConfig['displayKey'] && (this.optionList.length!==this.filteredOption.length))
      this.optionList = this.filteredOption;
      this.optionList = this._filter(this.inputValue || '');
  }

  /**
   * @description emits inputField value on input blur
   */
  emitBlur(){
    this.formValue = this.inputValue;
    this.onBlur.emit(this.formValue);
    this.markAsTouched();
  }
}
