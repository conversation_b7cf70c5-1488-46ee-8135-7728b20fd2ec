@import "../../../../assets/scss/styles.scss";

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.textbox-wrapper {
  display: flex;
  flex-direction: column;
  gap: 10px;
  font-family: $fontFamily;
  font-style: normal;
  line-height: normal;
  width: 100%;

  &.readOnly {
    .textbox-label, input , textarea{
      pointer-events: none;
    }
  }

  & > p, label {
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    color: var(--lib-color-black, #0c1315);

    span.field-error {
      color: var(--lib-color-red-2, #8e0000);
    }
  }
  .input-wrapper {
    position: relative;
    width: inherit;

    textarea {
      padding: 11px 16px;
      border-radius: 8px;
      resize: none;
      width: inherit;
      border: none;
      outline: 1px solid var(--lib-color-gray, #616161);
      background: var(--lib-color-white, #fff);
      letter-spacing: 0.5px;
      color: var(--lib-color-black, #0C1315);
      font-size: 14px;
      font-family: $fontFamily;

      &:hover {
        outline: 1px solid var(--lib-color-tertiary, #1d1878);
      }
      &:focus {
        outline: 1px solid var(--lib-color-gray, #1d1878);
      }
    }

    textarea:disabled {
      outline: 1px solid var(--lib-color-gray-4, #8d8d8d);
      color: 1px solid var(--lib-color-gray-4, #8d8d8d);
      background: var(--lib-color-gray-6, #e9e9e9);
    }

    textarea + .icon-wrapper {
      display: none;
    }

    input {
      padding: 12px 16px 12px 16px;
      border-radius: 8px;
      width: inherit;
      border: 1px solid var(--lib-color-gray, #616161);
      background: var(--lib-color-white, #fff);
      letter-spacing: 0.5px;
      color: var(--lib-color-black, #0C1315);
      font-size: 14px;
      font-family: $fontFamily;

      &:hover {
        border: 1px solid var(--lib-color-tertiary, #1d1878);
      }
      &:focus {
        border: 1px solid var(--lib-color-tertiary, #1d1878);
        outline: none;
      }
      &::placeholder {
        font-size: 14px;
        line-height: 100%;
        letter-spacing: 0px;
        color: var(--lib-color-gray, #616161);
        font-family: $fontFamily;
      }
    }

    input[type="password"] {
      padding: 2px 25px 4px 16px;
      letter-spacing: 2.5px;
      font-size: 28px;
      font-family: $fontFamily;
      

      &::placeholder {
        font-size: 14px;
        line-height: 100%;
        letter-spacing: 0px;
        color: var(--lib-color-gray, #616161);
        font-family: $fontFamily;
      }
    }

    input:disabled {
      border: 1px solid var(--lib-color-gray-4, #8d8d8d);
      color: var(--lib-color-gray-4, #8d8d8d);
      background: var(--lib-color-gray-6, #e9e9e9);
    }

    .icon-wrapper {
      position: absolute;
      right: 10px;
      top: 9px;
      display: flex;
      gap: 8px;

      i {
        font-size: 20px;
        cursor: pointer;
      }
    }
  }

  .helper-info {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    padding: 0 2px;
    font-weight: 400;
    font-family: $fontFamily;

    .disable {
      color: var(--lib-color-gray-4, #8d8d8d);
    }
  }
}

.success {
  // for textarea
  .input-wrapper textarea,
  .input-wrapper textarea:hover,
  .input-wrapper textarea:focus {
    outline: 1px solid var(--lib-color-green-2, #1c6f5a);
  }

  // for input
  .input-wrapper input,
  .input-wrapper input:hover {
    border: 1px solid var(--lib-color-green-2, #1c6f5a);
  }

  .input-wrapper input:focus {
    outline: 1px solid var(--lib-color-green-2, #1c6f5a);
    border: none;
  }

  .helper-info {
    color: var(--lib-color-green-2, #1c6f5a);
  }
}
.error {
  // for textarea
  .input-wrapper textarea,
  .input-wrapper textarea:hover,
  .input-wrapper textarea:focus {
    outline: 1px solid var(--lib-color-red-2, #8e0000);
  }

  // for input
  .input-wrapper input,
  .input-wrapper input:hover {
    border: 1px solid var(--lib-color-red-2, #8e0000);
  }

  .input-wrapper input:focus {
    border: 1px solid var(--lib-color-red-2, #8e0000);
  }

  .helper-info {
    color: var(--lib-color-red-2, #8e0000);
  }
}
.readOnly {
  // for textarea
  .input-wrapper textarea,
  .input-wrapper textarea:hover,
  .input-wrapper textarea:focus {
    background: var(--lib-color-gray-7, #fafafa);
    border: none;
    outline: none;
  }

  // for input
  .input-wrapper input,
  .input-wrapper input:hover {
    background: var(--lib-color-gray-7, #fafafa);
    border: none;
    outline: none;
    pointer-events: none;
  }
}

.option-dd {
  margin: 4px 8px;
}

//override angular material style
.asite-mat-option.mat-mdc-option:focus.mdc-list-item,
.asite-mat-option.mat-mdc-option.mat-mdc-option-active.mdc-list-item,
.asite-mat-option.mat-mdc-option:hover:not(.mdc-list-item--disabled),
.asite-mat-option.mat-mdc-option.mdc-list-item--selected:not(
    .mdc-list-item--disabled
  ):not(.mat-mdc-option-multiple) {
  background: var(--lib-color-purple-6, #cfcdf4);
  border-radius: 8px;
}

.asite-mat-option-img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 5px;
}

.unobserved {
  display: flex;
  margin: 0;
  padding: 0;
  border: 0;
  opacity: 1;
  clip-path: inset(100%);
  clip: rect(1px, 1px, 1px, 1px);
  background: transparent !important;
  overflow: hidden !important;
  width: 1px !important;
  height: 1px !important;
  position: absolute;
  top: 0;
  white-space: nowrap;
}
.unobserved.none {
  display: none;
}
@media print {
  .unobserved {
      display: none;
  }
}

