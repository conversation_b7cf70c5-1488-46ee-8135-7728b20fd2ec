import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TextboxComponent } from './textbox.component';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';

describe('TextboxComponent', () => {
  let component: TextboxComponent;
  let fixture: ComponentFixture<TextboxComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TextboxComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(TextboxComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('switchType :: should change type to password if it is text', () => {
    component.inputSettings.type = 'text';
    component.switchType();
    expect(component.inputSettings.type).toBe('password');
  });

  it('clearInput :: should clear input Value & formValue', () => {
    component.inputValue = 'text';
    component.clearInput();
    expect(component.inputValue).toBe('');
    expect(component.formValue).toBe('');
  });

  it('emitInputValue :: should store current inputvalue & update formValue & optionlist', () => {
    component.filteredOption = [
      { id: 1, label: 'ariel' },
      { id: 2, label: 'ariana' },
      { id: 4, label: 'text' },
    ];
    component.typeheadConfig = { displayKey: 'label' };
    component.inputValue = 'text';
    component.emitInputValue();

    expect(component.formValue).toBe(component.inputValue);
    expect(component.optionList.length).toBe(1);
    expect(component.optionList[0]['label']).toBe(component.inputValue);
  });

  it('ngOnChanges :: should update optionList if type head component and length of optionList and filterOptions is not same', () => {
    component.filteredOption = [
      { id: 1, label: 'ariel' },
      { id: 2, label: 'ariana' },
      { id: 4, label: 'text' },
    ];

    component.optionList = [
      { id: 2, label: 'ariana' },
      { id: 1, label: 'ariel' },
    ];
    component.typeheadConfig = { displayKey: 'label' };
    component.inputValue = 'text';
    component.ngOnChanges();
    expect(component.typeheadConfig).not.toBeUndefined();
  });

  it('writeValue :: should assign value to formValue', () => {
    component.writeValue('hello');
    expect(component.formValue).toBe('hello');
  });

  it('registerOnChange :: should assign value to onChange var', () => {
    let onChange = (value: string) => {};
    component.registerOnChange(onChange);
    expect(component.onChange).toBe(onChange);
  });

  it('registerOnTouch :: should assign value to onTouch var', () => {
    let onTouch = () => {};
    component.registerOnTouched(onTouch);
    expect(component.onTouched).toBe(onTouch);
  });

  it('markAsTouched :: touched var should be assign true', () => {
    component.markAsTouched();
    expect(component.touched).toBe(true);
  });

  it('optionSelected :: should assign emitted value from option selected', () => {
    const selectedValue = 'selected-value';
    const emitInputValueSpy = spyOn(component, 'emitInputValue');

    component.optionSelected({ option: { value: selectedValue } } as MatAutocompleteSelectedEvent);

    expect(component.inputValue).toBe(selectedValue);
    expect(emitInputValueSpy).toHaveBeenCalled();
  });

  it('emitBlur :: should emit input value on blur', () => {
    component.inputValue = 'test';
    spyOn(component, 'markAsTouched');
    component.emitBlur();
    expect(component.formValue).toEqual(component.inputValue);
    expect(component.markAsTouched).toHaveBeenCalled();
  });
});
