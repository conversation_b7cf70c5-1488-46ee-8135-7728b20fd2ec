import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ButtonComponent } from './button.component';
import {
  ButtonSize,
  ButtonSizeEnum,
  ButtonType,
  ButtonTypeEnum,
  SegmentedIconPosition,
  SegmentedIconPositionEnum,
} from './button.model';

describe('ButtonComponent', () => {
  let component: ButtonComponent;
  let fixture: ComponentFixture<ButtonComponent>;

  beforeEach(async () => {
    fixture = TestBed.createComponent(ButtonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

describe('Button Types Enums', () => {
  it('should have correct enum values', () => {
    expect(ButtonTypeEnum.Primary).toEqual('primary');
    expect(ButtonTypeEnum.Secondary).toEqual('secondary');
    expect(ButtonTypeEnum.Tertiary).toEqual('tertiary');
    expect(ButtonTypeEnum.Destructive_Primary).toEqual('destructive-primary');
    expect(ButtonTypeEnum.Destructive_Secondary).toEqual(
      'destructive-secondary'
    );
    expect(ButtonTypeEnum.Destructive_Tertiary).toEqual('destructive-tertiary');
    expect(ButtonTypeEnum.Segmented_Primary).toEqual('segmented-primary');
    expect(ButtonTypeEnum.Segmented_Secondary).toEqual('segmented-secondary');
    expect(ButtonTypeEnum.Round).toEqual('round');
    expect(ButtonTypeEnum.Rounded_Secondary).toEqual('round-secondary');
    expect(ButtonTypeEnum.Rounded_Tertiary).toEqual('round-tertiary');
    expect(ButtonTypeEnum.Rounded_Destructive_Primary).toEqual(
      'round-destructive-primary'
    );
    expect(ButtonTypeEnum.Rounded_Destructive_Secondary).toEqual(
      'round-destructive-secondary'
    );
    expect(ButtonTypeEnum.Rounded_Destructive_Tertiary).toEqual(
      'round-destructive-tertiary'
    );
  });
});

describe('Button Type Input', () => {
  it('should allow primary button type', () => {
    const buttonType: ButtonType = ButtonTypeEnum.Primary;
    expect(buttonType).toEqual(ButtonTypeEnum.Primary);
  });

  it('should allow secondary button type', () => {
    const buttonType: ButtonType = ButtonTypeEnum.Secondary;
    expect(buttonType).toEqual(ButtonTypeEnum.Secondary);
  });

  it('should allow tertiary button type', () => {
    const buttonType: ButtonType = ButtonTypeEnum.Tertiary;
    expect(buttonType).toEqual(ButtonTypeEnum.Tertiary);
  });

  it('should allow destructive primary button type', () => {
    const buttonType: ButtonType = ButtonTypeEnum.Destructive_Primary;
    expect(buttonType).toEqual(ButtonTypeEnum.Destructive_Primary);
  });

  it('should allow destructive secondary button type', () => {
    const buttonType: ButtonType = ButtonTypeEnum.Destructive_Secondary;
    expect(buttonType).toEqual(ButtonTypeEnum.Destructive_Secondary);
  });

  it('should allow destructive tertiary button type', () => {
    const buttonType: ButtonType = ButtonTypeEnum.Destructive_Tertiary;
    expect(buttonType).toEqual(ButtonTypeEnum.Destructive_Tertiary);
  });

  it('should allow segmented primary button type', () => {
    const buttonType: ButtonType = ButtonTypeEnum.Segmented_Primary;
    expect(buttonType).toEqual(ButtonTypeEnum.Segmented_Primary);
  });
  it('should allow segmented secondary button type', () => {
    const buttonType: ButtonType = ButtonTypeEnum.Segmented_Secondary;
    expect(buttonType).toEqual(ButtonTypeEnum.Segmented_Secondary);
  });
  it('should allow round button type', () => {
    const buttonType: ButtonType = ButtonTypeEnum.Round;
    expect(buttonType).toEqual(ButtonTypeEnum.Round);
  });
  it('should allow rounded secondary button type', () => {
    const buttonType: ButtonType = ButtonTypeEnum.Rounded_Secondary;
    expect(buttonType).toEqual(ButtonTypeEnum.Rounded_Secondary);
  });
  it('should allow rounded tertiary ter button type', () => {
    const buttonType: ButtonType = ButtonTypeEnum.Rounded_Tertiary;
    expect(buttonType).toEqual(ButtonTypeEnum.Rounded_Tertiary);
  });
  it('should allow rounded destructive primary button type', () => {
    const buttonType: ButtonType = ButtonTypeEnum.Rounded_Destructive_Primary;
    expect(buttonType).toEqual(ButtonTypeEnum.Rounded_Destructive_Primary);
  });
  it('should allow rounded destructive secondary button type', () => {
    const buttonType: ButtonType = ButtonTypeEnum.Rounded_Destructive_Secondary;
    expect(buttonType).toEqual(ButtonTypeEnum.Rounded_Destructive_Secondary);
  });
  it('should allow rounded destructive tertiary button type', () => {
    const buttonType: ButtonType = ButtonTypeEnum.Rounded_Destructive_Tertiary;
    expect(buttonType).toEqual(ButtonTypeEnum.Rounded_Destructive_Tertiary);
  });
});

describe('Button Sizes Enums', () => {
  it('should have correct enum values', () => {
    expect(ButtonSizeEnum.Small).toEqual('sm');
    expect(ButtonSizeEnum.Medium).toEqual('md');
    expect(ButtonSizeEnum.Large).toEqual('lg');
  });
});

describe('Button customWidth Input', () => {
  let component: ButtonComponent;
  beforeEach(() => {
    component = new ButtonComponent();
  });
  it('should call setCustomWidth when customWidth is given and Segmented_Primary', () => {    
    component.buttonType = ButtonTypeEnum.Segmented_Primary;
    component.customWidth = 144;
    let mockWidthObj = {
      "width": '100px'
    }
    const res = component.setCustomWidth();
    expect(res).toEqual(mockWidthObj);
  });
  it('should call setCustomWidth when customWidth is given and Segmented_Secondary', () => {    
    component.buttonType = ButtonTypeEnum.Segmented_Secondary;
    component.customWidth = 144;
    let mockWidthObj = {
      "width": '102px'
    }
    const res = component.setCustomWidth();
    expect(res).toEqual(mockWidthObj);
  });
  it('should call setCustomWidth when customWidth is given and no Segmented_Secondary/Segmented_Primary', () => {    
    component.buttonType = ButtonTypeEnum.Primary;
    component.customWidth = 144;
    let mockWidthObj = {
      "width": '144px'
    }
    const res = component.setCustomWidth();
    expect(res).toEqual(mockWidthObj);
  });
});

describe('Button Size Input', () => {
  it('should allow small button size', () => {
    const size: ButtonSize = ButtonSizeEnum.Small;
    expect(size).toEqual(ButtonSizeEnum.Small);
  });

  it('should allow medium button size', () => {
    const size: ButtonSize = ButtonSizeEnum.Medium;
    expect(size).toEqual(ButtonSizeEnum.Medium);
  });

  it('should allow large button size', () => {
    const size: ButtonSize = ButtonSizeEnum.Large;
    expect(size).toEqual(ButtonSizeEnum.Large);
  });
});

describe('Label Input', () => {
  let component: ButtonComponent;
  beforeEach(() => {
    component = new ButtonComponent();
  });

  it('should have default empty string label', () => {
    expect(component.label).toEqual('');
  });

  it('should set a valid label', () => {
    const validLabel = 'Button';
    component.label = validLabel;
    expect(component.label).toEqual(validLabel);
  });
});

describe('Disabled Input', () => {
  let component: ButtonComponent;
  beforeEach(() => {
    component = new ButtonComponent();
  });

  it('should have default value of false for disabled property', () => {
    expect(component.disabled).toBe(false);
  });

  it('should set disabled property to true', () => {
    component.disabled = true;
    expect(component.disabled).toBe(true);
  });

  it('should handle setting disabled property to false', () => {
    component.disabled = true;
    component.disabled = false;
    expect(component.disabled).toBe(false);
  });
});

describe('iconLeft and iconRight Input', () => {
  let component: ButtonComponent;
  beforeEach(() => {
    component = new ButtonComponent();
  });

  it('should have default value of an empty string for iconLeft property', () => {
    expect(component.iconLeft).toEqual('');
  });

  it('should have default value of an empty string for iconRight property', () => {
    expect(component.iconRight).toEqual('');
  });

  it('should set a valid iconLeft', () => {
    const validIcon = 'iconoir-lens';
    component.iconLeft = validIcon;
    expect(component.iconLeft).toEqual(validIcon);
  });
  it('should set a valid iconRight', () => {
    const validIcon = 'iconoir-lens';
    component.iconRight = validIcon;
    expect(component.iconRight).toEqual(validIcon);
  });
});

describe('segmentedIcon Input', () => {
  let component: ButtonComponent;
  beforeEach(() => {
    component = new ButtonComponent();
  });

  it('should have default value of an empty string for icon property', () => {
    expect(component.segmentedIcon).toEqual('');
  });

  it('should set a valid icon', () => {
    const validIcon = 'iconoir-lens';
    component.segmentedIcon = validIcon;
    expect(component.segmentedIcon).toEqual(validIcon);
  });
});

describe('Segmented Icon Position Enums', () => {
  it('should have correct enum values', () => {
    expect(SegmentedIconPositionEnum.Left).toEqual('left');
    expect(SegmentedIconPositionEnum.Right).toEqual('right');
  });
});

describe('segmented Icon Position Input', () => {
  let component: ButtonComponent;
  beforeEach(() => {
    component = new ButtonComponent();
  });

  it('should have default value of null for segmentedIconPosition property', () => {
    expect(component.segmentedIconPosition).toBeNull();
  });

  it('should set Icon Position property to left', () => {
    const validIconPosition: SegmentedIconPosition =
      SegmentedIconPositionEnum.Left;
    component.segmentedIconPosition = validIconPosition;
    expect(component.segmentedIconPosition).toEqual(validIconPosition);
  });

  it('should set Icon Position property to right', () => {
    const validIconPosition: SegmentedIconPosition =
      SegmentedIconPositionEnum.Right;
    component.segmentedIconPosition = validIconPosition;
    expect(component.segmentedIconPosition).toEqual(validIconPosition);
  });
});

describe('Button onClick Output', () => {
  let component: ButtonComponent;
  let fixture: ComponentFixture<ButtonComponent>;
  beforeEach(() => {
    fixture = TestBed.createComponent(ButtonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should emit onClick event', () => {
    let event: Event | undefined;
    component.onClick.subscribe((e) => (event = e));

    const button = fixture.debugElement.nativeElement.querySelector('button');
    button.click();
    expect(event).toBeDefined();
  });
});
