<div class="btn-main" [class]="segmentedIconPosition">
  <button
    role="button"
    tabindex="0"
    [attr.type]="type"
    (click)="onClick.emit($event)"
    [ngClass]="[buttonType, size, label != '' && iconLeft != '' || iconRight != '' ? 'withIcon' : label != '' && iconLeft == '' && iconRight == '' ? 'labelOnly' : 'iconOnly']"
    [disabled]="disabled"
    [attr.aria-label]="label"
    [attr.title]="title"
    [ngStyle]="setCustomWidth()"
    >
    <i [class]="iconLeft" aria-hidden="true" *ngIf="iconLeft" role="img"></i>
    <span *ngIf="label" class="button-label" [ngClass]="{'unobserved':!showLabel}">{{ label }}</span>
    <i [class]="iconRight" aria-hidden="true" *ngIf="iconRight" role="img"></i>
    <ng-content></ng-content>
    <span *ngIf="isLoading" class="button-loader" [ngClass]="'button-loader-' + size"></span>
  </button>
  <ng-container *ngIf="buttonType === typeOfButton.Segmented_Primary || buttonType === typeOfButton.Segmented_Secondary">
    <button
      role="button"
      tabindex="0"
      [attr.type]="type"
      aria-expanded="false"
      (click)="buttonClick.emit($event)"
      [ngClass]="[buttonType, size, segmentedIcon != '' ? 'iconOnly' : '']"
      [disabled]="disabled"
      [attr.aria-label]="label"
      [attr.title]="title"
      >
      <i [class]="segmentedIcon" aria-hidden="true" *ngIf="segmentedIcon" role="img"></i>
    </button>
  </ng-container>
</div>
