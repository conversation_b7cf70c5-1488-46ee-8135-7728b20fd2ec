import {
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectionStrategy,
} from '@angular/core';
import {
  ButtonSize,
  ButtonSizeEnum,
  ButtonType,
  ButtonTypeEnum,
  ButtonTypeHTMLEnum,
  SegmentedIconPosition,
} from './button.model';
import { CommonModule } from '@angular/common';

@Component({
  standalone: true,
  selector: 'lib-button',
  templateUrl: './button.component.html',
  styleUrls: ['./button.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule
  ],
})
export class ButtonComponent {
  /**
   * Specify the type of the button
   */
  @Input() buttonType: ButtonType = ButtonTypeEnum.Primary;
  typeOfButton = ButtonTypeEnum;

  @Input() type:string = ButtonTypeHTMLEnum.Button;

  /**
   * Button text label
   * @type Boolean
   */
  @Input() showLabel:boolean = true;

  /**
   * Specify the size of the button
   */
  @Input() size: ButtonSize = ButtonSizeEnum.Medium;

   /**
   * Specify the custom width of the button
   */
   @Input() customWidth: number;

  /**
   * Button label contents
   */
  @Input() label: string = '';

  @Input() disabled = false;

  @Input() iconLeft: string = '';

  @Input() iconRight: string = '';

  @Input() segmentedIcon: string = '';

  @Input() segmentedIconPosition: SegmentedIconPosition | null = null;

  @Input() title: string = '';

   /** @description Button loader flag */
  @Input() isLoading:boolean = false;

  /**
   * Button click handler
   */
  @Output() onClick = new EventEmitter<Event>();

  @Output() buttonClick = new EventEmitter<Event>();

  constructor() {}

  /**
   * Set custom width to button
   */
  setCustomWidth() {
    if (this.customWidth) {
      let widthObj = {
        "width": ''
      };
      let iconWidth = 0;

      if (this.buttonType === this.typeOfButton.Segmented_Primary || this.buttonType === this.typeOfButton.Segmented_Secondary) {
        if (this.buttonType === this.typeOfButton.Segmented_Primary) {
          iconWidth = this.size === 'sm' ? 34 : this.size === 'md' ? 44 : 54;
        } else {
          iconWidth = this.size === 'sm' ? 32 : this.size === 'md' ? 42 : 52;
        }
        widthObj.width = this.customWidth - iconWidth + "px";
        return widthObj;
      } else {
        widthObj.width = this.customWidth + "px";
        return widthObj;
      }
    }
    else {
      return null;
    }
  }
}
