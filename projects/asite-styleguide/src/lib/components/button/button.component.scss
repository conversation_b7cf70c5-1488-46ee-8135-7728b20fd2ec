@import "../../../../assets/scss/styles.scss";

// Button Size
$btn-sm: 0.875rem;
$btn-md: 1rem;
$btn-lg: 1.125rem;

$btn-sm-height: 32px;
$btn-md-height: 42px;
$btn-lg-height: 52px;

button {
  display: inline-flex;
  justify-content: center;
  font-family: $fontFamily;
  align-items: center;
  cursor: pointer;
  border: 0;
  border-radius: 8px;
  color: var(--lib-color-white, #ffffff);
  text-align: center;
  font-feature-settings: "clig" off, "liga" off;
  gap: 6px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;

  --loader-color: var(--lib-color-primary, #4940d7);

  &.primary {
    background: var(--lib-color-primary, #4940d7);
    --loader-color: var(--lib-color-white, #ffffff);

    &:hover,&:focus-visible {
      background: var(--lib-color-purple-3, #3d36b4);
    }

    &:active {
      background: var(--lib-color-purple-2, #352f9d);
    }

    &:disabled {
      background: var(--lib-color-gray-6, #e9e9e9);
      color: var(--lib-color-gray-5, #C5C5C5);
      --loader-color: var(--lib-color-primary, #4940d7);
      cursor: not-allowed;
    }
  }

  &.secondary, &.round-secondary {
    color: var(--lib-color-primary, #4940d7);
    outline: 1px solid var(--lib-color-tertiary, #1d1878);
    background-color: var(--lib-color-white, #ffffff);
    --loader-color: var(--lib-color-primary, #4940d7);

    &:hover,&:focus-visible {
      color: var(--lib-color-tertiary, #1d1878);
      outline: 1px solid var(--lib-color-tertiary, #1d1878);
      background-color: var(--lib-color-purple-6, #cfcdf4);
    }

    &:active {
      outline: 1px solid var(--lib-color-tertiary, #1d1878);
      background-color: var(--lib-color-secondary, #ecebfc);
    }

    &:disabled {
      outline: 1px solid var(--lib-color-gray-5, #C5C5C5);
      background-color: var(--lib-color-white, #ffffff);
      color: var(--lib-color-gray-5, #C5C5C5);
      --loader-color: var(--lib-color-primary, #4940d7);
      cursor: not-allowed;
    }
  }

  &.tertiary, &.round-tertiary {
    color: var(--lib-color-primary, #4940d7);
    background: inherit;
    --loader-color: var(--lib-color-primary, #4940d7);

    &:hover,&:focus-visible {
      background: var(--lib-color-purple-6, #cfcdf4);
    }

    &:active {
      background: var(--lib-color-secondary, #ecebfc);
    }

    &:disabled {
      background-color: var(--lib-color-white, #ffffff);
      color: var(--lib-color-gray-5, #c5c5c5);
      --loader-color: var(--lib-color-primary, #4940d7);
      cursor: not-allowed;
    }
  }

  &.destructive-primary, &.round-destructive-primary{
    color: var(--lib-color-red-1, #720000);
    background: var(--lib-color-red-8, #fff0ed);
    --loader-color: var(--lib-color-red-1, #720000);

    &:hover,&:focus-visible {
      background: var(--lib-color-red-7, #ffd1c9);
    }

    &:active {
      background: var(--lib-color-red-6, #ffb0a5);
    }

    &:disabled {
      background: var(--lib-color-gray-6, #e9e9e9);
      color: var(--lib-color-gray-5, #c5c5c5);
      --loader-color: var(--lib-color-red-1, #720000);
      cursor: not-allowed;
    }
  }

  &.destructive-secondary,&.round-destructive-secondary {
    color: var(--lib-color-red-1, #720000);
    outline: 1px solid var(--lib-color-red-1, #720000);
    background: var(--lib-color-white, #fff);
    --loader-color: var(--lib-color-red-1, #720000);

    &:hover,&:focus-visible {
      background: var(--lib-color-red-8, #fff0ed);
    }

    &:active {
      background: var(--lib-color-red-7, #ffd1c9);
    }

    &:disabled {
      color: var(--lib-color-gray-5, #c5c5c5);
      outline: 1px solid var(--lib-color-gray-5, #c5c5c5);
      background: var(--lib-color-white, #fff);
      cursor: not-allowed;
      --loader-color: var(--lib-color-red-1, #720000);
    }
  }

  &.destructive-tertiary,&.round-destructive-tertiary {
    color: var(--lib-color-red-1, #720000);
    background: inherit;
    --loader-color: var(--lib-color-red-1, #720000);

    &:hover,&:focus-visible {
      background: var(--lib-color-red-8, #fff0ed);
    }

    &:active {
      background: var(--lib-color-red-7, #ffd1c9);
    }

    &:disabled {
      background-color: var(--lib-color-white, #ffffff);
      color: var(--lib-color-gray-5, #c5c5c5);
      cursor: not-allowed;
      --loader-color: var(--lib-color-red-1, #720000);
    }
  }

  &.segmented-primary {
    color: var(--lib-color-white, #ffffff);
    background: var(--lib-color-primary, #4940d7);
    --loader-color: var(--lib-color-white, #ffffff);

    &:hover,&:focus-visible {
      background: var(--lib-color-purple-3, #3d36b4);
    }

    &:active {
      background: var(--lib-color-purple-2, #352f9d);
    }

    &:disabled {
      background: var(--lib-color-gray-6, #e9e9e9);
      color: var(--lib-color-gray-5, #c5c5c5);
      --loader-color: var(--lib-color-primary, #4940d7);
      cursor: not-allowed;
    }
  }

  &.segmented-secondary {
    color: var(--lib-color-primary, #4940d7);
    background: var(--lib-color-white, #ffffff);
    outline: 1px solid var(--lib-color-tertiary, #1d1878);
    --loader-color: var(--lib-color-primary, #4940d7);

    &:hover,&:focus-visible {
      color: var(--lib-color-tertiary, #1d1878);
      background: var(--lib-color-purple-6, #cfcdf4);
    }

    &:active {
      background: var(--lib-color-secondary, #ecebfc);
    }

    &:disabled {
      outline: 1px solid var(--lib-color-gray-5, #c5c5c5);
      background-color: var(--lib-color-white, #ffffff);
      color: var(--lib-color-gray-5, #c5c5c5);
      --loader-color: var(--lib-color-primary, #4940d7);
      cursor: not-allowed;
    }
  }

  &.round {
    border-radius: 30px;
    background: var(--lib-color-primary, #4940d7);
    --loader-color: var(--lib-color-white, #ffffff);

    &:hover,&:focus-visible {
      background: var(--lib-color-purple-3, #3d36b4);
    }

    &:active {
      background: var(--lib-color-purple-2, #352f9d);
    }

    &:disabled {
      background: var(--lib-color-gray-6, #e9e9e9);
      color: var(--lib-color-gray-5, #C5C5C5);
      --loader-color: var(--lib-color-primary, #4940d7);
      cursor: not-allowed;
    }
  }

  &.round-secondary,&.round-tertiary,
  &.round-destructive-primary,
  &.round-destructive-secondary,
  &.round-destructive-tertiary{
    border-radius: 30px;
  }
  &.sm {
    font-size: $btn-sm;

    &.labelOnly {
      padding: 0px 12px;

      .button-label{
        height: $btn-sm-height;
      }
    }

    &.withIcon {
      i {
        font-size: 1.125rem;
        height: $btn-sm-height;
      }
      padding: 0px 12px;
      .button-label {
        height: $btn-sm-height;
      }
    }

    &.iconOnly {
      font-size: 1.125rem;
      padding: 7px;
    }
  }

  &.md {
    font-size: $btn-md;

    &.labelOnly {
      padding: 0px 14px;
      .button-label{
        height: $btn-md-height;
      }
    }

    &.withIcon {
      i {
        font-size: 1.25rem;
        height: $btn-md-height;
      }

      padding: 0px 14px;
      .button-label {
        height: $btn-md-height;
      }
    }

    &.iconOnly {
      font-size: 1.25rem;
      padding: 11px;
    }

  }

  &.lg {
    font-size: $btn-lg;

    &.labelOnly {
      padding: 0px 18px;

      .button-label{
        height: $btn-lg-height;
      }
    }

    &.withIcon {
      i {
        font-size: 1.5rem;
        height: $btn-lg-height;
      }
      padding: 0px 16px;
      .button-label {
        height: $btn-lg-height;
      }
    }

    &.iconOnly {
      font-size: 1.5rem;
      padding: 14px;
    }
  }


  &.right {
    flex-direction: row-reverse;
  }

  .button-label {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: flex;
    align-items: center;
    justify-content: center;

    .unobserved {
      display: flex;
      margin: 0;
      border: 0;
      opacity: 0.1;
      clip-path: inset(100%);
      clip: rect(1px, 1px, 1px, 1px);
      background: transparent !important;
      overflow: hidden !important;
      width: 1px !important;
      height: 1px !important;
      position: absolute;
      white-space: no-wrap;
    }
  }

  i {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .button-loader {
    border: 2px solid var(--loader-color);
    border-bottom-color: transparent;
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: rotation 1s linear infinite;

    &.button-loader-sm {
      width: 18px;
      height: 18px;
    }

    &.button-loader-md {
      width: 20px;
      height: 20px;
    }

    &.button-loader-lg {
      width: 24px;
      height: 24px;
    }

    @keyframes rotation {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }
  }
}

.btn-main {
  display: flex;

  button.segmented-primary {
    border-radius: 8px 0px 0px 8px;
    border-right: 0.5px solid var(--lib-color-white, #ffffff);

    &:disabled {
      border-right: 0.5px solid var(--lib-color-gray-5, #c5c5c5);
      border-left: none;
    }

    &.iconOnly {
      border-radius: 0px 8px 8px 0px;
      border-left: 0.5px solid var(--lib-color-white, #ffffff);

      &:disabled {
        border-left: 0.5px solid var(--lib-color-gray-5, #c5c5c5);
        border-right: none;
      }
    }
  }

  button.segmented-secondary {
    border-radius: 8px 0px 0px 8px;

    &.iconOnly {
      border-radius: 0px 8px 8px 0px;
    }

  }

  &.left {
    flex-direction: row-reverse;
    justify-content: flex-end;

    button.segmented-primary {
      border-radius: 0px 8px 8px 0px;
      border-left: 0.5px solid var(--lib-color-white, #ffffff);

      &:disabled {
        border-left: 0.5px solid var(--lib-color-gray-5, #c5c5c5);
        border-right: none;
      }

      &.iconOnly {
        border-radius: 8px 0px 0px 8px;
        border-right: 0.5px solid var(--lib-color-white, #ffffff);

        &:disabled {
          border-right: 0.5px solid var(--lib-color-gray-5, #c5c5c5);
          border-left: none;
        }
      }
    }

    button.segmented-secondary {
      border-radius: 0px 8px 8px 0px;

      &.iconOnly {
        border-radius: 8px 0px 0px 8px;
      }
    }
  }
}
