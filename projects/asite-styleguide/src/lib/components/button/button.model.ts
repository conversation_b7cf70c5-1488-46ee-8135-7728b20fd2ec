export enum ButtonTypeHTMLEnum {
  Button = 'button',
  Submit = 'submit',
  Cancel = 'cancel',
}
export type ButtonTypeHTML =
  | ButtonTypeHTMLEnum.Button
  | ButtonTypeHTMLEnum.Submit
  | ButtonTypeHTMLEnum.Cancel;

export enum ButtonSizeEnum {
  Small = 'sm',
  Medium = 'md',
  Large = 'lg',
}
export type ButtonSize =
  | ButtonSizeEnum.Small
  | ButtonSizeEnum.Medium
  | ButtonSizeEnum.Large;

export enum ButtonTypeEnum {
  Primary = 'primary',
  Secondary = 'secondary',
  Tertiary = 'tertiary',
  Destructive_Primary = 'destructive-primary',
  Destructive_Secondary = 'destructive-secondary',
  Destructive_Tertiary = 'destructive-tertiary',
  Segmented_Primary = 'segmented-primary',
  Segmented_Secondary = 'segmented-secondary',
  Round = 'round',
  Rounded_Secondary = 'round-secondary',
  Rounded_Tertiary = 'round-tertiary',
  Rounded_Destructive_Primary = 'round-destructive-primary',
  Rounded_Destructive_Secondary = 'round-destructive-secondary',
  Rounded_Destructive_Tertiary = 'round-destructive-tertiary',
}

export type ButtonType =
  | ButtonTypeEnum.Primary
  | ButtonTypeEnum.Secondary
  | ButtonTypeEnum.Tertiary
  | ButtonTypeEnum.Destructive_Primary
  | ButtonTypeEnum.Destructive_Secondary
  | ButtonTypeEnum.Destructive_Tertiary
  | ButtonTypeEnum.Segmented_Primary
  | ButtonTypeEnum.Segmented_Secondary
  | ButtonTypeEnum.Round
  | ButtonTypeEnum.Rounded_Secondary
  | ButtonTypeEnum.Rounded_Tertiary
  | ButtonTypeEnum.Rounded_Destructive_Primary
  | ButtonTypeEnum.Rounded_Destructive_Secondary
  | ButtonTypeEnum.Rounded_Destructive_Tertiary;

export enum SegmentedIconPositionEnum {
  Left = 'left',
  Right = 'right',
}

export type SegmentedIconPosition =
  | SegmentedIconPositionEnum.Left
  | SegmentedIconPositionEnum.Right;
