@import '../../../../assets/scss/styles.scss';

.breadcrumb-bar {
  display: flex;
  align-items: center;
  font-family: $fontFamily;
  font-size: 14px;
  line-height: 1;
  padding: 4px 0;
}

.breadcrumb-list {
  display: flex;
  align-items: center;
  list-style: none;
  padding: 0;
  margin: 0;
  gap: 4px;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.breadcrumb-link,
.breadcrumb-current {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  color: var(--lib-color-black, #0C1315);
  text-decoration: none;

  span {
    padding-top: 4px;
    max-width: 190px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    vertical-align: middle;
  }
}

.breadcrumb-link {
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
  width: 100%;
}

.breadcrumb-current {
  cursor: default;
}

.breadcrumb-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.breadcrumb-separator {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #888;
}
