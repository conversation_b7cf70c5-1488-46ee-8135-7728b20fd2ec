<!-- breadcrumb.component.html -->
<nav class="breadcrumb-bar" aria-label="Breadcrumb">
    <ol class="breadcrumb-list">
        @for (crumb of breadcrumbItems; track crumb.level; let i = $index; let last = $last) {
        <li class="breadcrumb-item" [attr.aria-current]="last && !single ? 'page' : null">
            @if (!last || single) {
            <a class="breadcrumb-link" (click)="onClick(i, $event)" href="javascript:void(0);" [title]="crumb.label">
                @if (crumb.icon) {
                <i class="breadcrumb-icon" [ngClass]="crumb.icon" aria-hidden="true"></i>
                }
                <span>{{ crumb.label }}</span>
            </a>

            @if (!last) {
                <span class="breadcrumb-separator" aria-hidden="true">
                <i class="iconoir-slash"></i>
                </span>
            }
            } @else {
            <span class="breadcrumb-current">
                @if (crumb.icon) {
                <i class="breadcrumb-icon" [ngClass]="crumb.icon" aria-hidden="true"></i>
                }
                <span>{{ crumb.label }}</span>
            </span>
            }
        </li>
        }
    </ol>
</nav>
