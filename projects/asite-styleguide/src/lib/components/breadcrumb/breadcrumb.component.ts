import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Breadcrumb } from './breadcrumb.model';

@Component({
    selector: 'lib-breadcrumb',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './breadcrumb.component.html',
    styleUrls: ['./breadcrumb.component.scss']
})

export class BreadcrumbComponent {

  @Input() breadcrumbItems: Breadcrumb[] = [];

  @Output() breadcrumbClicked = new EventEmitter<{index: number, event: MouseEvent}>();
  
  constructor() {      
  }

  get single(): boolean {
  return this.breadcrumbItems.length === 1;
  }

  trackByLevel(index: number, crumb: Breadcrumb) {
    return crumb.level;
  }

  onClick(index: number, event: MouseEvent) {
    this.breadcrumbClicked.emit({index, event});
  }
}
