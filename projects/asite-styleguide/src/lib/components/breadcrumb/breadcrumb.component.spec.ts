import { ComponentFixture, TestBed } from '@angular/core/testing';

import { BreadcrumbComponent } from './breadcrumb.component';
import { By } from '@angular/platform-browser';
import { Breadcrumb } from './breadcrumb.model';

describe('BreadcrumbComponent', () => {
  let component: BreadcrumbComponent;
  let fixture: ComponentFixture<BreadcrumbComponent>;

  const MULTI: Breadcrumb[] = [
    { label: 'HOME', level: 'HOME', icon: 'icon-home' },
    { label: 'Projects', level: 'PROJECT', icon: 'icon-project' },
  ];

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [BreadcrumbComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(BreadcrumbComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should report single = true when only one item', () => {
    component.breadcrumbItems = [
      { label: 'Home', level: 'HOME' },
    ];
    fixture.detectChanges();
    expect(component.single).toBeTrue();
  });

  it('should render the same number of .breadcrumb-item elements as breadcrumbItems', () => {
    component.breadcrumbItems = MULTI;
    fixture.detectChanges();

    const items = fixture.debugElement.queryAll(By.css('.breadcrumb-item'));
    expect(items.length).toBe(MULTI.length);
  });

  it('should set aria-current="page" on the last crumb when list has > 1 item', () => {
    component.breadcrumbItems = MULTI;
    fixture.detectChanges();

    const els = fixture.debugElement.queryAll(By.css('.breadcrumb-item'));

    expect(els[0].attributes['aria-current']).toBeUndefined();
    expect(els[1].attributes['aria-current']).toBe('page');
  });

  it('should not set aria-current when there is only one crumb', () => {
    component.breadcrumbItems = [
      { label: 'Home', level: 'HOME' },
    ];
    fixture.detectChanges();

    const el = fixture.debugElement.query(By.css('.breadcrumb-item'));
    expect(el.attributes['aria-current']).toBeUndefined();
  });

  it('should emit breadcrumbClicked with correct index and event on link click', () => {
    const clickSpy = jasmine.createSpy('breadcrumbClickSpy');
    component.breadcrumbClicked.subscribe(clickSpy);

    component.breadcrumbItems = MULTI;
    fixture.detectChanges();

    const firstLink = fixture.debugElement.query(By.css('.breadcrumb-link'));
    firstLink.nativeElement.click();

    expect(clickSpy).toHaveBeenCalledTimes(1);
    expect(clickSpy).toHaveBeenCalledWith(
      jasmine.objectContaining({
        index: 0,
        event: jasmine.any(MouseEvent),
      })
    );
  });

  it('trackByLevel should return the crumb level', () => {
    const crumb: Breadcrumb = { label: 'Home', level: 'HOME' };
    expect(component.trackByLevel(42, crumb)).toBe('HOME');
  });
});
