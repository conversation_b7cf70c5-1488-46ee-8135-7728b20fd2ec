export enum ToastSizeEnum {
  Small = 'sm',
  Big = 'bg',
}

export type ToastSize = ToastSizeEnum.Small | ToastSizeEnum.Big;

export enum ToastTypeEnum {
  Info = 'info',
  Success = 'success',
  Warning = 'warning',
  Error = 'error',
}

export type ToastType = ToastTypeEnum.Info | ToastTypeEnum.Success | ToastTypeEnum.Warning | ToastTypeEnum.Error;

export enum ToastPositionEnum {
  TopLeft = 'toast-top-left',
  TopRight =  'toast-top-right',
  BottomLeft = 'toast-bottom-left',
  BottomRight = 'toast-bottom-right',
}

export type ToastPositionType = ToastPositionEnum.TopLeft | ToastPositionEnum.TopRight | ToastPositionEnum.BottomLeft | ToastPositionEnum.BottomRight;


export interface UniquePosition {
  position: ToastPositionEnum;
  notifications: any[];
}

export interface ToastOptionsInterface {
  isButton?: boolean;
  size?: string;
  hyperlinkText?: string;
  callback?: () => void;
}

/**
 * title: string;
 * @title The title of the toast notification.
 */
export interface ToastInterface
{
  uid: string;
  message: string;
  type: ToastType;
  timer: any;
  position: string;
  autoHide?: boolean;
  isButton?: boolean;
  size?: string;
  hyperlinkText?: string;
  title?: string;
  callback?: () => void;
}
