import {
  Component,
  ContentChild,
  EventEmitter,
  Input,
  Output,
  TemplateRef,
} from '@angular/core';
import {
  ToastInterface,
  ToastPositionEnum,
  ToastSize,
  ToastSizeEnum,
  ToastType,
  ToastTypeEnum,
  UniquePosition,
} from './notification-toast.model';
import { ButtonComponent } from '../button/button.component';
import { CommonModule } from '@angular/common';
import { ToastService } from '../../services/toast.service';
import { ButtonSizeEnum, ButtonTypeEnum } from '../index';

@Component({
  standalone: true,
  selector: 'lib-notification-toast',
  templateUrl: './notification-toast.component.html',
  styleUrls: ['./notification-toast.component.scss'],
  imports: [CommonModule, ButtonComponent],
})
export class NotificationToast {
  @Input()
  type: ToastType = ToastTypeEnum.Info;

  /**
   * Specify the size of the Toast
   */
  @Input()
  size: ToastSize = ToastSizeEnum.Small;

  /**
   * Toast message contents
   *
   * @required
   */

  icon: string = '';

  @Input() hyperlinkText: string = 'Hyperlink';

  @Input() url: string = 'https://www.asite.com';

  @Input() isButton: boolean = false;

  @Input() delayAfterMouseLeave: number;

  @ContentChild('ToastTemplate') ToastTemplate: TemplateRef<any> | null = null;

  /**
   * Optional click handler
   */
  @Output() onCloseClick = new EventEmitter<Event>();

  @Output() onButtonClick = new EventEmitter<Event>();

  /** asite style guide button configurations */
  buttonType = ButtonTypeEnum;

  buttonSize = ButtonSizeEnum;

  uniquePositions: UniquePosition[] = [
    {
      position: ToastPositionEnum.TopLeft,
      notifications: [],
    },
    {
      position: ToastPositionEnum.TopRight,
      notifications: [],
    },
    {
      position: ToastPositionEnum.BottomLeft,
      notifications: [],
    },
    {
      position: ToastPositionEnum.BottomRight,
      notifications: [],
    },
  ];

  constructor(private _toastService: ToastService) {}

  ngOnInit(): void {
    this._toastService.toast$.subscribe((messages) => {
      this.showToast(messages);
    });
  }

  showToast(toasts: ToastInterface[]): void {
    this.uniquePositions[0].notifications = [];
    this.uniquePositions[1].notifications = [];
    this.uniquePositions[2].notifications = [];
    this.uniquePositions[3].notifications = [];

    for (const element of toasts) {
      switch (element.position) {
        case ToastPositionEnum.TopLeft: {
          this.uniquePositions[0].notifications.push(element);
          break;
        }
        case ToastPositionEnum.TopRight: {
          this.uniquePositions[1].notifications.push(element);
          break;
        }
        case ToastPositionEnum.BottomLeft: {
          this.uniquePositions[2].notifications.push(element);
          break;
        }
        case ToastPositionEnum.BottomRight: {
          this.uniquePositions[3].notifications.push(element);
          break;
        }
      }
    }
  }

  closeToast(toast: ToastInterface, event: Event): void {
    this._toastService.removeToast(toast.uid);
    /* Close Toast event for external use */
    this.onCloseClick.emit(event);
  }

  /* Close toast on Mouse Enter event */
  onMouseEnter(toast: ToastInterface) {
    this._toastService.resetToastTimer(toast.uid);
  }
  /* Close toast on Mouse Leave */
  onMouseLeave(toast: ToastInterface) {
    if(!toast.autoHide) { return; }
    if(this.delayAfterMouseLeave){
      setTimeout(() => {
        this._toastService.removeToast(toast.uid);
      }, this.delayAfterMouseLeave);
    } else {
      this._toastService.removeToast(toast.uid);
    }
  }

  /* Close toast on click of hyperlink */
  onLinkClick(toast: ToastInterface) {
    toast.callback();
    this._toastService.removeToast(toast.uid);
  }
}
