@import "../../../../assets/scss/styles.scss";


/* Mixins for animations */
@mixin toastAnimation($position) {
  @if $position == 'top-left' {
    animation: SlideInLeft 0.5s ease forwards, fadeOut 0.5s ease forwards;
  }
  @else if $position == 'top-right' {
    animation: SlideInRight 0.5s ease forwards, fadeOut 0.5s ease forwards;
  }
  @else if $position == 'bottom-left' {
    animation: SlideInLeftBottom 0.5s ease forwards, fadeOut 0.5s ease forwards;
  }
  @else if $position == 'bottom-right' {
    animation: SlideInRightBottom 0.5s ease forwards, fadeOut 0.5s ease forwards;
  }
}



/* Individual Toast Container */
.toast-container {
  position: fixed;
  display: flex;
  flex-direction: column;
  z-index: 99999;
  &.toast-top-left {
    top: 12px;
    left: 12px;
    flex-direction: column;
    align-items: flex-start;
    .asite-toast{
      @include toastAnimation('top-right');
    }
  }
  &.toast-top-right {
    top: 12px;
    right: 12px;
    flex-direction: column;
    align-items: flex-end;
    .asite-toast{
      @include toastAnimation('top-left');
    }
  }
  &.toast-bottom-right {
    bottom: 12px;
    right: 12px;
    flex-direction: column;
    align-items: flex-end;
    .asite-toast{
      @include toastAnimation('bottom-left');
    }
  }
  &.toast-bottom-left {
    bottom: 12px;
    left: 12px;
    flex-direction: column;
    align-items: flex-start;
    .asite-toast{
      @include toastAnimation('bottom-right');
    }
  }

  .asite-toast {
    border-radius: 12px;
    display: flex;
    justify-content: space-between;
    pointer-events: auto;


    .toast-icon-wrapper{
      display: flex;
      padding: 14px 0px;
      justify-content: center;
      align-items: center;
      gap: 8px;

      .icon {
        font-size: 1.5rem;

        &.info {
          color: var(--lib-color-tertiary, #1D1878);
        }

        &.success {
          color: var(--green-darken-40, #1C6F5A);
        }

        &.warning {
          color: var(--lib-color-orange-2, #974801);
        }

        &.error {
          color: var(--lib-color-red-2, #8E0000);
        }
      }
    }

    .toast-title {
      color: var(--lib-color-black, #0C1315);
      font-feature-settings: 'clig' off, 'liga' off;
      font-family: $fontFamily;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
      text-transform: capitalize;
    }

    &.sm {
      height: auto;
      width: 600px;
      padding: 0px 20px;
      align-items: center;
      gap: 16px;

      .toast-wrapper {
        display: flex;
        width: 100%;
        gap: 16px;
      }

      .toast-content-wrapper {
        display: flex;
        padding: 15px 0px;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
        flex: 1 0 0;
      }

      .toast-template-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 16px;
        padding: 8px 0px;
      }
    }

    &.bg {
      height: auto;
      width: 331px;
      padding: 10px 20px;
      align-items: flex-start;
      gap: 20px;

      .icon {
        justify-content: flex-start;
      }

      .toast-wrapper {
        display: flex;
        width: 100%;
        gap: 16px;
        flex-direction: column;
        align-items: baseline;
        padding: 14px 0px;
      }

      .toast-content-wrapper{
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 11px;
        flex: 1 0 0;
      }

      .toast-template-wrapper {
        display: flex;
        flex-direction: column;

        /* Style for Hyperlink button */
        .hyperlink-button {
          display: flex;
          padding: 4px 0px;
          align-items: flex-start;
          gap: 10px;
          text-decoration: underline;
          color: var(--lib-color-primary, #4940D7);
          .hyperlink-text{
            color: var(--Product-Primary---Lilac, #4940D7);
            font-feature-settings: 'clig' off, 'liga' off;
            font-family: $fontFamily;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            text-decoration-line: underline;
          }
        }
        .hyperlink-button:hover{
          cursor: pointer;
        }
      }
    }

    &.info {
      outline: 1px solid var(--lib-color-tertiary, #1D1878);
      background: var(--lib-color-secondary, #ECEBFC);
    }

    &.success {
      outline: 1px solid var(--green-darken-40, #1C6F5A);
      background: var(--green-lighten-50, #F0FAF7);
    }

    &.warning {
      outline: 1px solid var(--lib-color-orange-2, #974801);
      background: var(--lib-color-orange-8, #FFF5E7);
    }

    &.error {
      outline: 1px solid var(--lib-color-red-2, #8E0000);
      background: var(--lib-color-red-8, #FFF0ED);
    }

    .close-icon-wrapper{
      .iconoir-xmark {
        font-size: 1rem;
        display: flex;
        padding: 8px 0px;
        justify-content: center;
        align-items: center;
        gap: 16px;
        &:hover {
          cursor: pointer;
        }
      }
    }
  }

  .asite-toast.close{
    animation: SlideOutLeft 0.5s ease forwards, fadeOut 0.5s ease forwards;
  }
}



@keyframes SlideInLeft {
  from {
      transform: translateX(calc(100% + 32px));
      opacity: 0;
  }
  to {
      opacity: 1;
      transform: translateX(0);
  }
}

@keyframes SlideInRight {
  from {
      transform: translateX(calc(-100% - 32px));
      opacity: 0;
  }
  to {
      opacity: 1;
      transform: translateX(0);
  }
}

@keyframes SlideInRightBottom {
  from {
      transform: translate(calc(-100% - 32px), 100%);
      opacity: 0;
  }
  to {
      opacity: 1;
      transform: translate(0, 0);
  }
}

@keyframes SlideInLeftBottom {
  from {
      transform: translate(calc(100% + 32px), 100%);
      opacity: 0;
  }
  to {
      opacity: 1;
      transform: translate(0, 0);
  }
}

