<ng-container *ngFor="let position of uniquePositions">
  <div class="toast-container" [ngClass]="position.position">
    <div *ngFor="let item of position.notifications;let i=index" class="asite-toast" [ngClass]="[item.type, item.size ? item.size : size]"
      (mouseenter)="onMouseEnter(item)" (mouseleave)="onMouseLeave(item)">
      <div class="toast-icon-wrapper">
        <i class="icon" [ngClass]="{
        'iconoir-info-circle info': item.type === 'info',
        'iconoir-check-circle success': item.type === 'success',
        'iconoir-warning-triangle warning': item.type === 'warning',
        'iconoir-warning-hexagon error': item.type === 'error'
      }"></i>
      </div>
      <div class="toast-wrapper" [style]="isButton ? 'gap: 11px;' : 'gap: 0;'">
        <div class="toast-content-wrapper">
          <span class="toast-title">{{ item?.title ? item.title : item.type }}:</span>
          <div [innerHTML]="item.message"></div>
        </div>
        <div class="toast-template-wrapper">
          <lib-button *ngIf="isButton && size !== 'bg'" [buttonType]="buttonType.Secondary" label="Button"
            [size]="buttonSize.Small" (onClick)="onButtonClick.emit($event);" class="toast-button"></lib-button>
          <!-- dynamic hyperlink button for large toast -->
          <span class="hyperlink-button" *ngIf="isButton && size !== 'sm'">
            <a class="hyperlink-text" [href]="url" target="_blank">{{ hyperlinkText }}</a>
          </span>
          <span class="hyperlink-button" *ngIf="item.isButton && item.size !== 'sm'">
            <div class="hyperlink-text" (click)="onLinkClick(item)">{{ item.hyperlinkText }}</div>
          </span>
        </div>
      </div>
      <div class="close-icon-wrapper">
        <i class="iconoir-xmark" (click)="closeToast(item,$event,i)"></i>
      </div>
    </div>
  </div>
</ng-container>