import { ComponentFixture, fakeAsync, flush, TestBed, tick } from '@angular/core/testing';
import { NotificationToast } from './notification-toast.component';
import { ToastInterface, ToastSize, ToastSizeEnum, ToastType, ToastTypeEnum } from './notification-toast.model';
import { Component, TemplateRef, ViewChild } from '@angular/core';
import { ToastService } from '../../services/toast.service';
import { ToastPositionEnum } from 'asite-styleguide';

describe('NotificationToastComponent', () => {
  let component: NotificationToast;
  let fixture: ComponentFixture<NotificationToast>;

  beforeEach(async () => {
    fixture = TestBed.createComponent(NotificationToast);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

describe('NotificationToast Types Enums', () => {
  it('should have correct enum values', () => {
    expect(ToastTypeEnum.Info).toEqual('info');
    expect(ToastTypeEnum.Success).toEqual('success');
    expect(ToastTypeEnum.Warning).toEqual('warning');
    expect(ToastTypeEnum.Error).toEqual('error');
  });
});

describe('NotificationToast Type Input', () => {
  it('should allow Info NotificationToast type', () => {
    const ToastType: ToastType = ToastTypeEnum.Info;
    expect(ToastType).toEqual(ToastTypeEnum.Info);
  });

  it('should allow Success NotificationToast type', () => {
    const ToastType: ToastType = ToastTypeEnum.Info;
    expect(ToastType).toEqual(ToastTypeEnum.Info);
  });

  it('should allow Warning NotificationToast type', () => {
    const ToastType: ToastType = ToastTypeEnum.Warning;
    expect(ToastType).toEqual(ToastTypeEnum.Warning);
  });

  it('should allow Danger NotificationToast type', () => {
    const ToastType: ToastType = ToastTypeEnum.Error;
    expect(ToastType).toEqual(ToastTypeEnum.Error);
  });
});

describe('NotificationToast Sizes Enums', () => {
  it('should have correct enum values', () => {
    expect(ToastSizeEnum.Small).toEqual('sm');
    expect(ToastSizeEnum.Big).toEqual('bg');
  });
});

describe('NotificationToast Size Input', () => {
  it('should allow Small NotificationToast size', () => {
    const size: ToastSize = ToastSizeEnum.Small;
    expect(size).toEqual(ToastSizeEnum.Small);
  });

  it('should allow Big NotificationToast size', () => {
    const size: ToastSize = ToastSizeEnum.Big;
    expect(size).toEqual(ToastSizeEnum.Big);
  });

});

describe('NotificationToast Icon', () =>
{
  let component: NotificationToast;
  let toastService: ToastService;
  beforeEach(() => {
    component = new NotificationToast(toastService);
    component.icon = 'iconoir-circle';
  });

  it('should have icon property as a string', () => {
    expect(typeof component.icon).toBe('string');
  });
});

describe('NotificationToast Button', () => {
  let component: NotificationToast;
  let toastService: ToastService;
  beforeEach(() => {
    component = new NotificationToast(toastService);
    component.isButton = true;
  });

  it('should have isButton property as a boolean', () => {
    expect(typeof component.isButton).toBe('boolean');
  });

  it('should have isButton set to true or false', () => {
    expect(component.isButton === true || component.isButton === false).toBeTruthy();
  });

  it('should show the button if isButton is true', () => {
    expect(component.isButton).toBe(true);
  });

  it('should not show the button if isButton is false', () => {
    component.isButton = false;
    expect(component.isButton).toBe(false);
  });

});

describe('NotificationToast hyperlinkText', () => {
  let component: NotificationToast;
  let toastService: ToastService;
  beforeEach(() => {
    component = new NotificationToast(toastService);
    component.hyperlinkText = 'Hyperlink';
  });

  it('should have hyperlinkText property as a string', () => {
    expect(typeof component.hyperlinkText).toBe('string');
  });
});

describe('NotificationToast url', () => {
  let component: NotificationToast;
  let toastService: ToastService;
  beforeEach(() => {
    component = new NotificationToast(toastService);
    component.url = 'https://asite.com';
  });

  it('should have url property as a string', () => {
    expect(typeof component.url).toBe('string');
  });
});

/* tested template Reference propery in NotificationToast component */

@Component({
  template: `
    <lib-notification-toast>
      <ng-template #ToastTemplate>mock Content</ng-template>
    </lib-notification-toast>
  `,
})
class TestComponent {
  @ViewChild('ToastTemplate') ToastTemplate!: TemplateRef<any>;
}

describe('NotificationToast TemplateRef', () => {
  let component: NotificationToast;
  let fixture: ComponentFixture<TestComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TestComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TestComponent);
    component = fixture.debugElement.children[0].componentInstance as any;
    fixture.detectChanges();
  });

  it('should set template references', () => {
    const ToastTemplateRef: TemplateRef<any> = component.ToastTemplate;
    expect(ToastTemplateRef).toBeTruthy();
  });
});


describe('NotificationToast showToast', () => {
  let component: NotificationToast;
  let fixture: ComponentFixture<NotificationToast>;
  let _toastService: any;

  beforeEach(() => {
    fixture = TestBed.createComponent(NotificationToast);
    component = fixture.componentInstance;
    _toastService = TestBed.inject(ToastService);
    spyOn(component, 'showToast').and.callThrough();
    fixture.detectChanges();
  });

  it('should call the showToast method', () => {
    const mockToasts = [{
      uid:'Mb4pyPwWny',
      message: 'Test Message',
      type: ToastTypeEnum.Error,
      timer: 123,
      position:ToastPositionEnum.TopLeft
    },{
      uid:'sXmSrN4chd',
      message: 'Test Message',
      type: ToastTypeEnum.Error,
      timer: 123,
      position:ToastPositionEnum.BottomLeft
      }, {
      uid:'o1QzZe3Q1h',
      message: 'Test Message',
      type: ToastTypeEnum.Error,
      timer: 123,
      position:ToastPositionEnum.TopRight
      }, {
        uid:'GxSxqcJgfC',
        message: 'Test Message',
        type: ToastTypeEnum.Error,
        timer: 123,
        position:ToastPositionEnum.BottomRight
      }
    ];

    component.showToast(mockToasts);
    expect(component.showToast).toHaveBeenCalledWith(mockToasts);
  });
});

/* toast methods which call service methods */

describe('NotificationToast closeToast', () => {
  let component: NotificationToast;
  let fixture: ComponentFixture<NotificationToast>;
  let _toastService: any;

  beforeEach(() => {
    fixture = TestBed.createComponent(NotificationToast);
    component = fixture.componentInstance;
    _toastService = TestBed.inject(ToastService);
    spyOn(_toastService, 'removeToast').and.callThrough();
    spyOn(component.onCloseClick, 'emit').and.callThrough();
    fixture.detectChanges();
  });

  it('should remove toast and emit onCloseClick event when closeToast is called', () => {
    const mockToast :ToastInterface = {
      uid:'testid',
      message: 'Test Message',
      type: ToastTypeEnum.Error,
      timer: 123,
      position:ToastPositionEnum.TopLeft,
    };
    const mockUId = 'testid';
    const mockEvent = {} as Event;
    component.closeToast(mockToast, mockEvent);

    expect(_toastService.removeToast).toHaveBeenCalledWith(mockUId);
    expect(component.onCloseClick.emit).toHaveBeenCalledWith(mockEvent);
  });
});

describe('NotificationToast onMouseEnter', () => {
  let component: NotificationToast;
  let fixture: ComponentFixture<NotificationToast>;
  let _toastService: any;

  beforeEach(() => {
    fixture = TestBed.createComponent(NotificationToast);
    component = fixture.componentInstance;
    _toastService = TestBed.inject(ToastService);
    spyOn(_toastService, 'resetToastTimer').and.callThrough();
    fixture.detectChanges();
  });

  it('should reset toast timer when onMouseEnter is called', () => {
    const mockToast = {
      uid:'test12',
      message: 'Test Message',
      type: ToastTypeEnum.Error,
      timer: 123,
      position:ToastPositionEnum.BottomRight,
    };
    const mockUId = 'test12';

    component.onMouseEnter(mockToast);

    expect(_toastService.resetToastTimer).toHaveBeenCalledWith(mockUId);
  });
});

describe('NotificationToast onMouseLeave', () => {
  let component: NotificationToast;
  let fixture: ComponentFixture<NotificationToast>;
  let _toastService: any;

  beforeEach(() => {
    fixture = TestBed.createComponent(NotificationToast);
    component = fixture.componentInstance;
    _toastService = TestBed.inject(ToastService);
    spyOn(_toastService, 'removeToast').and.callThrough();
    fixture.detectChanges();
  });

  it('should reset toast timer when onMouseLeave is called', () => {
    const mockToast = {
      uid:'sXmSrN4chd',
      message: 'Test Message',
      type: ToastTypeEnum.Error,
      timer: 123,
      position:ToastPositionEnum.BottomLeft,
      autoHide: true
    };
    const mockUId = 'sXmSrN4chd';

    component.onMouseLeave(mockToast);

    expect(_toastService.removeToast).toHaveBeenCalledWith(mockUId);
  });
});

describe('AutoHide Toast', () => {
  let component: NotificationToast;
  let fixture: ComponentFixture<NotificationToast>;
  let _toastService: ToastService;

  beforeEach(() => {
    fixture = TestBed.createComponent(NotificationToast);
    component = fixture.componentInstance;
    _toastService = TestBed.inject(ToastService);
    spyOn(component, 'onMouseLeave').and.callThrough();
    spyOn(component, 'onLinkClick').and.callThrough();
    spyOn(_toastService, 'showToast').and.callThrough();
    spyOn(_toastService, 'removeToast').and.callThrough();
    fixture.detectChanges();
  });

  it('should not autoHide toast when onMouseLeave is called with autoHide=false', () => {
    const mockMessage = 'Test Message';
    const mockType = ToastTypeEnum.Success;
    const mockDuration = 5000;

    _toastService.showToast(mockMessage, mockType, mockDuration, ToastPositionEnum.TopRight, false);
    fixture.detectChanges();;

    let toastElement = fixture.debugElement.nativeElement.querySelector('.asite-toast');

    // Create a fake mouseleave event
    const event = new Event('mouseleave');

    // Dispatch the event on the toast element
    toastElement.dispatchEvent(event);

    fixture.detectChanges();

    // Verify that the onMouseLeave was called
    expect(component.onMouseLeave).toHaveBeenCalled();
    
    expect(_toastService.removeToast).not.toHaveBeenCalled();

    fixture.detectChanges();

    // Check whether toast element is not removed from DOM
    toastElement = fixture.debugElement.nativeElement.querySelector('.asite-toast');
    expect(toastElement).toBeTruthy();
  });

  it('should autoHide toast when onMouseLeave is called with autoHide=true', () => {
    const mockMessage = 'Test Message';
    const mockType = ToastTypeEnum.Success;
    const mockDuration = 5000;

    _toastService.showToast(mockMessage, mockType, mockDuration);
    fixture.detectChanges();;

    let toastElement = fixture.debugElement.nativeElement.querySelector('.asite-toast');

    // Create a fake mouseleave event
    const event = new Event('mouseleave');

    // Dispatch the event on the toast element
    toastElement.dispatchEvent(event);

    fixture.detectChanges();

    // Verify that the onMouseLeave and removeToast methods were called
    expect(component.onMouseLeave).toHaveBeenCalled();
    expect(_toastService.removeToast).toHaveBeenCalled();

    fixture.detectChanges();

    // Check whether toast element is removed from DOM
    toastElement = fixture.debugElement.nativeElement.querySelector('.asite-toast');
    expect(toastElement).toBe(null);
  });

  it('should autoHide toast after some delay when onMouseLeave is called with autoHide=true and has delayAfterMouseLeave', fakeAsync(() => {
    const mockMessage = 'Test Message';
    const mockType = ToastTypeEnum.Success;
    const mockDuration = 5000;
    component.delayAfterMouseLeave = 3000;

    _toastService.showToast(mockMessage, mockType, mockDuration);
    fixture.detectChanges();

    let toastElement = fixture.debugElement.nativeElement.querySelector('.asite-toast');
    const event = new Event('mouseleave');
    toastElement.dispatchEvent(event);
    fixture.detectChanges();
    expect(_toastService.removeToast).not.toHaveBeenCalled();

    const startTime = Date.now();
    tick(component.delayAfterMouseLeave);
    const endTime = Date.now();
    fixture.detectChanges();
    flush();

    expect(_toastService.removeToast).toHaveBeenCalled();
    expect(endTime - startTime).toBeGreaterThanOrEqual(component.delayAfterMouseLeave);
  }));

  it('should remove toast on click of hyperlink', () => {
    const mockMessage = 'Test Message';
    const mockType = ToastTypeEnum.Success;
    const mockDuration = 5000;
    const mockOptions = {
      isButton: true,
      size: ToastSizeEnum.Big,
      hyperlinkText: 'copy',
      callback: () => {}
    }

    _toastService.showToast(mockMessage, mockType, mockDuration, ToastPositionEnum.TopRight, false, mockOptions);
    fixture.detectChanges();

    let linkElement = fixture.debugElement.nativeElement.querySelector('.hyperlink-text');

    // Create a fake click event
    const event = new MouseEvent('click', { bubbles: true, cancelable: true });

    // Dispatch the event on the link element
    linkElement.dispatchEvent(event);

    fixture.detectChanges();

    // Verify that the onLinkClick and removeToast methods were called
    expect(component.onLinkClick).toHaveBeenCalled();
    expect(_toastService.removeToast).toHaveBeenCalled();

    fixture.detectChanges();

    // Check whether toast element is removed from DOM
    let toastElement = fixture.debugElement.nativeElement.querySelector('.asite-toast');
    expect(toastElement).toBe(null);
  });
});
