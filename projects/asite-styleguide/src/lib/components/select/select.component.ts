import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  OnChanges,
  Output,
  EventEmitter,
  HostListener,
  OnInit,
  ViewChild,
} from '@angular/core';
import { Option, OptionData } from './select.model';
import {
  ControlValueAccessor,
  FormsModule,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatMenuModule } from '@angular/material/menu';
import { MatButtonModule } from '@angular/material/button';
import { ButtonComponent, ButtonTypeEnum, SearchBarComponent } from '../index';

@Component({
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule, MatMenuModule, MatButtonModule, SearchBarComponent, ButtonComponent],
  selector: 'lib-select',
  templateUrl: './select.component.html',
  styleUrls: ['./select.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: SelectComponent,
      multi: true,
    },
  ],
})
export class SelectComponent implements OnChanges, OnInit, ControlValueAccessor {

  /**
   * @description to support search functionality in dd.
   * @type {boolean}
   */
  @Input() isSearchEnable = false;

  /*** @description asite-styleguide button configuration. */
  buttonType = ButtonTypeEnum;

  /*** @description holds the searched text. */
  searchText: string = '';

  @Input() panelClass: string = '';

  /**
   * @description to render different types of dropdown state
   * @type {string}
   */
  @Input() dropdownState = 'default'; //only give default state available ('success','error','readOnly','disabled');


  /**
   * @description to support multi and single selection of dd
   * @type {boolean}
   */

  @Input() isMultiSelect = false;

  /**
 * @description Prevents clearing the selected option in single-select mode.
 * When true, the user must choose another option to change the selection; 
 * deselecting without selecting another is not allowed.
 */
  @Input() preventSelectionClear = false;

  /**
   * @description gets label & option data from parent component
   * @type {Object}
   */

  @Input() optionsData: OptionData = {
    optionList: [],
    placeholder: 'Select Options',
    isRequired: false,
    selectId: 'input'
  };
  /**
   * @description two way binds dropdown value
   * @type {Object}
   */
  @Input() dropdownVal: number | number[] = [];


  /**
   * @description holds the IDs of options that should be disabled
   * @type {Array<number>}
   */
  @Input() disabledOptionsIds = [];

  /**
   * @description emits object of selected DD options
   * @type {Array or number}
   */
  @Output() dropdownValChange = new EventEmitter<any>();

  /**
   * @description Emits when menu open
   * @memberof SelectComponent
   */
  @Output() dropdownOpened = new EventEmitter<any>();

  /**
   * @description Searchbar component reference
   * @type {SearchBarComponent}
   * @memberof SelectComponent
   */
  @ViewChild('searchBar') searchBar!: SearchBarComponent;

  /**
   * @description holds value of selected options
   */
  public selectedOpt: Option[] = [];

  /**
   * @description holds value of available options
   */
  public availableOptions: Option[] = [];

  /**
   * @description holds value of dropdownState & changes color accordingly
   */
  public dropdownColor = this.dropdownState;

  public labelColor = '';

  /**
   * @description holds display value of dropdown
   */
  public displayValue = '';

  /**
   * @description toggles dropdown menu
   */
  public openDD: boolean = false;

  //variable & function for reactive form support
  onChange = (value: number[] | number) => { };

  onTouched = () => { };

  formValue: number[] | number = [];

  touched = false;

  // varibale to set dropdown width same as its input width
  dropdownWidth: number = 400;

  /**
   * @description this function open/close dropdown based on click
   */
  public dropdownEvent() {
    this.openDD = !this.openDD;

    if (this.dropdownState === 'readOnly' || this.dropdownState === 'disabled')
      this.openDD = false;

    this.clearSearchbarValue();
  }

  /**
   * @description selects value & emits data
   * @param option item to be selected
   */
  public selectOption(option: Option, onSelect: boolean, event?: Event) {
    if (!this.isMultiSelect) {
      if (this.selectedOpt.length){
        this.availableOptions.push(this.selectedOpt[0]);
      }
      this.selectedOpt = [];
    }

    this.availableOptions.forEach((element: Option, i: number) => {
      if (option.id === element.id) {
        this.selectedOpt.push(option);
        this.availableOptions.splice(i, 1);
      }
    });

    this.selectedOpt = [...this.selectedOpt];
    this.availableOptions = [...this.availableOptions];
    this.filteredOptions = [...this.availableOptions];

    this.displayTextEmitValue(onSelect);

    if (!this.isMultiSelect) {
      this.openDD = false;
    }

    if (this.isMultiSelect) {
      event && event.stopPropagation();
    }
  }

  /**
   * @description unselects value & emits data
   * @param option item to be unSelected
   */
  public deselectItem(item: Option, onSelect: boolean, event: Event) {
    // Prevents deselection if `preventSelectionClear` is true and not in multi-select mode.
    if (this.preventSelectionClear && !this.isMultiSelect) {
      return;
    }

    this.selectedOpt.forEach((element: any, i: number) => {
      if (item.id === element.id) this.selectedOpt.splice(i, 1);
    });

    if (!this.availableOptions.includes(item)) this.availableOptions.push(item);

    this.selectedOpt = [...this.selectedOpt];
    this.availableOptions = [...this.availableOptions];
    this.filteredOptions = [...this.availableOptions];

    this.displayTextEmitValue(onSelect);

    if (!this.isMultiSelect) {
      this.openDD = false;
    }

    if (this.isMultiSelect) {
      event && event.stopPropagation();
    }
  }

  /**
   * @description changes border color acc to state
   * @param state state name to be applied
   */
  public DDBorderColor(state: string) {
    if (
      this.dropdownState == 'success' ||
      this.dropdownState == 'error' ||
      this.dropdownState == 'disabled' ||
      this.dropdownState == 'readOnly'
    )
      return this.dropdownState;

    if (this.selectedOpt.length) return 'filled';
    else return state;
  }

  /**
   * @description changes label color acc to state
   */
  public labelTextColor(): string {
    switch (this.dropdownState) {
      case 'success':
        return 'success-label';
      case 'error':
        return 'error-label';
      case 'disabled':
        return 'disabled-label';
      default:
        return '';
    }
  }

  /**
   * @description when input data changes
   */
  public defaultOption() {
    this.selectedOpt = [];
    this.availableOptions = [...this.optionsData.optionList];
    this.filteredOptions = [...this.availableOptions]; // Initialize filtered options

    let isSelected = this.availableOptions.filter((val: Option) => {
      if (typeof this.dropdownVal != 'object') {
        return this.dropdownVal === val.id;
      }
      return this.dropdownVal && this.dropdownVal.includes(val.id);
    });

    if (isSelected.length > 1 && !this.isMultiSelect) return;

    for (let item of isSelected) {
      this.selectOption(item, false);
    }

    if (!isSelected.length) this.displayTextEmitValue(false);

    if (this.isSearchEnable && this.searchText) {
      this.searchTextChangedEvent(this.searchText);
    }
  }

  /**
   * @description modify displayvalue and updates form value
   */
  public displayTextEmitValue(onSelect: boolean) {
    let labelArr = [];
    for (let value of this.selectedOpt) labelArr.push(value.label);

    if (this.selectedOpt.length) {
      this.displayValue = labelArr.join(', ');
      this.dropdownColor = this.DDBorderColor('filled');
    } else {
      this.displayValue = this.optionsData['placeholder'];
      this.dropdownColor = this.dropdownState;
    }

    this.labelColor = this.labelTextColor();

    let valueEmit: number | number[] = this.selectedOpt.map((item) => item.id);
    if (!this.isMultiSelect) valueEmit = valueEmit.length ? valueEmit[0] : -1;
    onSelect && this.dropdownValChange.emit(valueEmit);
    this.formValue = valueEmit;
    this.onChange(this.formValue);
    this.markAsTouched();
  }

  public ngOnChanges(): void {
    this.defaultOption();
    this.dropdownColor = this.DDBorderColor(this.dropdownState);
    this.labelColor = this.labelTextColor();
  }

  public ngOnInit(): void {
    this.getDropdownWidth();
  }

  public keydownOnMenu(event: KeyboardEvent): void {
    if (event.key == 'Escape') {
      this.openDD = false;
      this.clearSearchbarValue();
    }
  }

  // when  click outside dropdown menu
  @HostListener('document:click', ['$event'])
  handleClick(event: Event) {
    this.openDD = false;
  }

  //functions for reactive form value support
  writeValue(value: number[] | number) {
    this.formValue = value;
  }

  registerOnChange(onChange: any) {
    this.onChange = onChange;
  }

  registerOnTouched(onTouched: any) {
    this.onTouched = onTouched;
  }

  markAsTouched() {
    if (!this.touched) {
      this.onTouched();
      this.touched = true;
    }
  }

  getDropdownWidth() {
    let inputElement = document.getElementById(this.optionsData.selectId || 'input');
    if (!this.openDD && inputElement) {
      this.dropdownWidth = inputElement.offsetWidth;
    }
  }

  public filteredOptions: Option[] = [];

  /*** @description triggers on search input. */
  searchTextChangedEvent(value: string) {
    this.searchText = value;
    if (this.searchText) {
      this.filteredOptions = this.availableOptions.filter(option =>
        option.label.toLowerCase().includes(this.searchText.toLowerCase())
      );
    } else {
      this.filteredOptions = [...this.availableOptions];
    }
  }

  /*** @description clear options selection. */
  clearSelection() {
    this.availableOptions = [...this.availableOptions, ...this.selectedOpt];
    this.selectedOpt = [];
    this.displayTextEmitValue(true);
  }

  /*** @description clear searchbar value on open and close of Dropdown. */
  clearSearchbarValue() {
    if (this.isSearchEnable && this.searchText) {
      this.searchText = '';
      this.searchTextChangedEvent(this.searchText);
      this.searchBar.clearSearchText();
    }
  }

  /**
   * @description Trigger on menu open
   * @memberof SelectComponent
   */
  onDropdownOpened() {
    let selectedOpt = {};
    if(!this.isMultiSelect) {
      selectedOpt = this.selectedOpt[0];
    }
    this.dropdownOpened.emit(selectedOpt);
  }
}
