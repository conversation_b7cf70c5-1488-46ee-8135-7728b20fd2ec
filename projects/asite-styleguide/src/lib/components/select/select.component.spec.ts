import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SelectComponent } from './select.component';
import { OptionData } from './select.model';
import { By } from '@angular/platform-browser';
import { SearchBarComponent } from '../search-bar/search-bar.component';
import { EventEmitter } from '@angular/core';

describe('SelectComponent', () => {
  let component: SelectComponent;
  let fixture: ComponentFixture<SelectComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SearchBarComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(SelectComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('dropdownEvent :: should close the list & openDD should be false', () => {
    component.dropdownState = 'disabled';
    component.dropdownEvent();
    expect(component.openDD).toBe(false);
  });

  it('should hide dropdowns when clicking outside the component', () => {
    // Simulate a click outside the component
    const documentClickEvent = new MouseEvent('click', { bubbles: true });
    document.dispatchEvent(documentClickEvent);
    fixture.detectChanges();
    expect(component.openDD).toBe(false);
  });

  it('labelTextColor :: should return style object', () => {
    const mockdata = ['success', 'error', 'disabled'];
    const expectedResult = ['success-label', 'error-label', 'disabled-label'];

    for (let i = 0; i < mockdata.length; i++) {
      component.dropdownState = mockdata[i];
      const res = component.labelTextColor();
      expect(res).toBe(expectedResult[i]);
    }
  });

  it('DDBorderColor :: should return same parameter assign', () => {
    const res = component.DDBorderColor('default');
    expect(res).toBe('default');
  });

  it('DDBorderColor :: should return style object', () => {
    component.dropdownState = 'success';
    const res = component.DDBorderColor('default');
    expect(res).toBe(component.dropdownState);
  });

  it('selectOption :: should selection option label when dropdown is single selection', () => {
    const mockdata = { id: 1, label: 'option 1' };
    component.selectedOpt = [{ id: 2, label: 'option 1' }];
    component.availableOptions = [];
    component.isMultiSelect = false;
    component.selectOption(mockdata, true, event);
    expect(component.selectedOpt.length).toBe(0);
  });

  it('selectOption :: should call event.stopPropagation when isMultiSelect is true', () => {
    const mockEvent: any = { stopPropagation: jasmine.createSpy('stopPropagation') };
    component.isMultiSelect = true;
    const mockdata = { id: 1, label: 'option 1' };
    component.selectedOpt = [];
    component.availableOptions = [{ id: 1, label: 'option 1' }, { id: 2, label: 'option 2' }];
    component.selectOption(mockdata, true, mockEvent);
    expect(mockEvent.stopPropagation).toHaveBeenCalled();
  });

  it('deSelectItem :: should deselect chosen options', () => {
    component.selectedOpt = [{ id: 2, label: 'option 1' }];
    const mockdata = { id: 2, label: 'option 1' };
    component.availableOptions = [];
    component.isMultiSelect = false;
    component.deselectItem(mockdata, true, event);
    expect(component.selectedOpt.length).toBe(0);
  });

  it('deselectItem :: should call event.stopPropagation when isMultiSelect is true', () => {
    const mockEvent: any = { stopPropagation: jasmine.createSpy('stopPropagation') };
    component.isMultiSelect = true;
    const mockItem = { id: 1, label: 'option 1' };
    component.selectedOpt = [{ id: 1, label: 'option 1' }];
    component.availableOptions = [{ id: 2, label: 'option 2' }];
    component.filteredOptions = [...component.availableOptions];
    component.deselectItem(mockItem, true, mockEvent);
    expect(mockEvent.stopPropagation).toHaveBeenCalled();
  });


  it('ngOnChanges :: should close dropdown list', () => {
    component.optionsData = {
      optionList: [{ id: 1, label: 'options 1' }],
      placeholder: 'select',
    };
    component.ngOnChanges();
    expect(component.openDD).toBe(false);
  });

  it('defaultOption :: should trigger selectOption ', () => {
    component.isMultiSelect = false;
    component.dropdownVal = 1;
    component.optionsData = {
      optionList: [{ id: 1, label: 'options 1' }],
      placeholder: 'select',
    };
    component.defaultOption();
    expect(component.selectedOpt.length).toBe(1);
  });

  it('defaultOption :: should return empty when optionList is empty ', () => {
    component.optionsData = {
      optionList: [],
      placeholder: 'select',
    };
    component.defaultOption();
    expect(component.selectedOpt.length).toBe(0);
  });

  it('defaultOption :: should return empty when dropdownVal in invalid', () => {
    component.optionsData.optionList = [{label:'option',id:1},{id:2,label:'op'}];
    component.dropdownVal = [1,2];
    component.defaultOption();
    expect(component.selectedOpt.length).toBe(0);
  });

  it('defaultOption :: should return empty when optionList size is more than 1 & mutltiSelect is false ', () => {
    component.optionsData = {
      optionList: [
        { id: 1, label: 'options 1' },
        { id: 2, label: 'options 2' },
      ],
      placeholder: 'select',
    };
    component.isMultiSelect = false;
    component.defaultOption();
    expect(component.selectedOpt.length).toBe(0);
  });

  it('writeValue :: should assign value to formValue', () => {
    component.writeValue(1);
    expect(component.formValue).toBe(1);
  });

  it('registerOnChange :: should assign value to onChange var', () => {
    let onChange = (value: number[]) => {};
    component.registerOnChange(onChange);
    expect(component.onChange).toBe(onChange);
  });

  it('registerOnTouch :: should assign value to onTouch var', () => {
    let onTouch = () => {};
    component.registerOnTouched(onTouch);
    expect(component.onTouched).toBe(onTouch);
  });

  it('should close the dropdown when Escape key is pressed', () => {
    component.openDD = true;
    const event = new KeyboardEvent('keydown', { key: 'Escape' });
    component.keydownOnMenu(event);
    expect(component.openDD).toBe(false);
  });

  it('should not close the dropdown for other keys', () => {
    component.openDD = true;
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    component.keydownOnMenu(event);
    expect(component.openDD).toBe(true);
  });

  it('searchTextChangedEvent should filter options based on search text', () => {
    const mockOptionList = [
      { id: 1, label: 'option 1' },
      { id: 2, label: 'option 2' },
      { id: 3, label: 'option 3' },
    ];

    component.availableOptions = [...mockOptionList];

    component.searchTextChangedEvent('opt');
    expect(component.filteredOptions).toEqual(mockOptionList);
  });

  it('searchTextChangedEvent should return all options when search text is empty', () => {
    const mockOptionList = [
      { id: 1, label: 'option 1' },
      { id: 2, label: 'option 2' },
      { id: 3, label: 'option 3' },
    ];

    component.availableOptions = [...mockOptionList];

    component.searchTextChangedEvent('');
    expect(component.filteredOptions).toEqual(mockOptionList);
  });

  it('clearSelection should call displayTextEmitValue with true', () => {
    spyOn(component, 'displayTextEmitValue');
    component.clearSelection();
    expect(component.displayTextEmitValue).toHaveBeenCalledWith(true);
  });

  it('clearSearchbarValue :: should clear searchText, emit event, and call searchBar.clearSearchText when search is enabled and searchText is present', () => {
    component.isSearchEnable = true;
    component.searchText = 'some text';
    spyOn(component, 'searchTextChangedEvent');
  
    // Mocking searchBar with clearSearchText spy
    component.searchBar = { clearSearchText: jasmine.createSpy('clearSearchText') } as any;
  
    component.clearSearchbarValue();
  
    expect(component.searchText).toBe('');
    expect(component.searchTextChangedEvent).toHaveBeenCalledWith('');
    expect(component.searchBar.clearSearchText).toHaveBeenCalled();
  });
  
  it('clearSearchbarValue :: should not clear searchText or call methods when search is disabled or searchText is empty', () => {
    component.isSearchEnable = false;
    component.searchText = '';
    spyOn(component, 'searchTextChangedEvent');
  
    component.searchBar = { clearSearchText: jasmine.createSpy('clearSearchText') } as any;
  
    component.clearSearchbarValue();
  
    expect(component.searchTextChangedEvent).not.toHaveBeenCalled();
    expect(component.searchBar.clearSearchText).not.toHaveBeenCalled();
  });

  describe('onDropdownOpened()', () => {
    beforeEach(() => {
      component.dropdownOpened = new EventEmitter<any>();
      spyOn(component.dropdownOpened, 'emit');
    });

    it('should emit first selected option if isMultiSelect is false', () => {
      component.isMultiSelect = false;
      component.selectedOpt = [{ id: 1, label: 'Option 1' }];

      component.onDropdownOpened();

      expect(component.dropdownOpened.emit).toHaveBeenCalledWith(component.selectedOpt[0]);
    });

    it('should emit empty object if isMultiSelect is true', () => {
      component.isMultiSelect = true;
      component.selectedOpt = [{ id: 1, label: 'Option 1' }];

      component.onDropdownOpened();

      expect(component.dropdownOpened.emit).toHaveBeenCalledWith({});
    });
  });
});
