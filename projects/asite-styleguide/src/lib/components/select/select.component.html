<div class="dropdown-wrapper" (click)=$event.stopPropagation()>
    <p *ngIf="optionsData['label']">{{optionsData['label']}} <span *ngIf="optionsData.isRequired || false"
            style="color:red">&#42;</span></p>
    <div [matMenuTriggerFor]="menu" class="dropdown-input" id="{{optionsData.selectId || 'input'}}" (click)="getDropdownWidth(); dropdownEvent()" 
        [ngClass]="dropdownColor" tabindex="0" (menuOpened)="onDropdownOpened()">
        <p [ngClass]="{'dropdown-image-text': !isMultiSelect && selectedOpt[0]?.img}"><img *ngIf="!isMultiSelect && selectedOpt[0]?.img" [src]="selectedOpt[0]?.img" [alt]="displayValue"> {{displayValue || optionsData['placeholder']}}</p>
        <i *ngIf="dropdownState !== 'readOnly'" class="iconoir-nav-arrow-right rotate" [ngClass]="{'down':openDD}"></i>
    </div>
    <mat-menu #menu="matMenu" class="customizeMenu">
        <div class="dropdown-list" [ngClass]="panelClass" [style.top]="optionsData['label'] ?'76px':'50px'" [ngStyle]="{'width': dropdownWidth ? dropdownWidth + 'px' : 'inherit'}" (keydown)="keydownOnMenu($event)">
            <div class="search-pannel" *ngIf="isSearchEnable">
                <lib-search-bar #searchBar [ngStyle]="{'width': '100%'}" [placeholderText]="'Search for...'"
                    (searchTextChangedEvent)="searchTextChangedEvent($event)" (click)="$event.stopPropagation()"
                    (keydown)="$event.stopPropagation()">
                </lib-search-bar>
                <lib-button [buttonType]="buttonType.Tertiary" [label]="'Clear'" [ngStyle]="{'margin' : '0 auto'}"
                    (click)="clearSelection(); $event.stopPropagation()">
                </lib-button>
            </div>
            <ng-container *ngFor='let item of selectedOpt'>
                <button *ngIf="!item.isHideOption" mat-menu-item (click)="deselectItem(item, true, $event)" [disabled]="disabledOptionsIds.includes(item.id)" class="dd-option">
                    <p class="option isSelected" [ngClass]="{'dropdown-image-text': item.img}"><img *ngIf="item.img" [src]="item.img" [alt]="item.label"> {{item.label}}</p>
                </button>
            </ng-container>
            <ng-container *ngFor='let item of filteredOptions'>
                <button *ngIf="!item.isHideOption" mat-menu-item (click)="selectOption(item, true, $event)" [disabled]="disabledOptionsIds.includes(item.id)" class="dd-option">
                    <p class="option" [ngClass]="{'dropdown-image-text': item.img}"><img *ngIf="item.img" [src]="item.img" [alt]="item.label"> {{item.label}}</p>
                </button>
            </ng-container>
        </div>
    </mat-menu>
    <div class="helper-info" *ngIf="optionsData['infoText']" [ngClass]="labelColor">
        <p>{{optionsData['infoText']}}</p>
        <i *ngIf="dropdownState === 'success'" class="iconoir-check-circle"></i>
        <i *ngIf="dropdownState === 'error'" class="iconoir-warning-circle"></i>
    </div>
</div>