@import "../../../../assets/scss/styles.scss";
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.dropdown-wrapper {
  display: flex;
  position: relative;
  flex-direction: column;
  gap: 11px;
  width: 100%;
  font-family: $fontFamily;
  font-style: normal;
  line-height: normal;
  > p{
    font-weight: 600;
  }

  .dropdown-input {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: inherit;
    height: 42px;
    border: 1px solid var(--lib-color-gray-4, #8d8d8d);
    border-radius: 8px;
    padding: 9px 8px 9px 16px;
    cursor: pointer;

    &:hover {
      border: 1px solid var(--lib-color-tertiary, #1d1878);
    }
    &:active, &:focus {
      outline: none;
      border: 1px solid var(--lib-color-purple-1, #302b7e);
    }

    p {
      font-size: 14px;
      font-weight: 400;
      background-color: inherit;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-family: $fontFamily;

      &.dropdown-image-text {
          display: flex;
          align-items: center;
          gap: 8px;
      
          img {
            width: 20px;
            height: 20px;
            object-fit: contain;
          }
      }
    }

    i {
      font-size: 24px;
    }
    .rotate {
      -moz-transition: all 0.15s linear;
      -webkit-transition: all 0.15s linear;
      transition: all 0.15s linear;
    }
    .rotate.down {
      -moz-transform: rotate(90deg);
      -webkit-transform: rotate(90deg);
      transform: rotate(90deg);
    }
  }

  .dropdown-input.disabled, .dropdown-input.readOnly {
    pointer-events: none;
  }

  .success {
    border-color: var(--lib-color-green-2, #1c6f5a);
    background-color: var(--lib-color-white, #fff);
    &:hover {
      border-color: var(--lib-color-green-2, #1c6f5a);
    }
  }

  .error {
    border-color: var(--lib-color-red-2, #8e0000);
    background-color: var(--lib-color-white, #fff);
    &:hover {
      border-color: var(--lib-color-red-2, #8e0000);
    }
  }

  .disabled {
    border-color: var(--lib-color-gray-4, #8d8d8d);
    background-color: var(--lib-color-gray-6, #e9e9e9);
    color: var(--lib-color-gray-4, #8d8d8d);
    &:hover {
      border-color: var(--lib-color-gray-4, #8d8d8d);
    }
  }
  .readOnly {
    border-color: var(--lib-color-gray-7, #fafafa);
    background-color: var(--lib-color-gray-7, #fafafa);
    &:hover {
      border-color: var(--lib-color-gray-7, #fafafa);
    }
  }

  .filled {
    color: var(--lib-color-black, #0c1315);
  }

  .default {
    background-color: var(--lib-color-white, #fff);
    color: var(--lib-color-gray, #616161);
  }

  .helper-info {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    padding: 0 2px;
    font-family: $fontFamily;
  }

  .success-label {
    color: var(--lib-color-green-2, #1c6f5a);
  }
  .error-label {
    color: #8e0000;
  }
  .disabled-label {
    color: var(--lib-color-gray-4, #8d8d8d);
  }
}

.search-pannel {
  display: flex;
  gap: 15px;
  padding: 4px 8px;
}
