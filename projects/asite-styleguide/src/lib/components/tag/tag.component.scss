@import "../../../../assets/scss/styles.scss";

// Tag Size
$tag-sm: 0.75rem;
$tag-md: 0.875rem;

.tag-container{
  display: flex;
  align-items: center;
  gap: 3px;
  .tag {
    font-family: $fontFamily;
    display: inline-flex;
    cursor: pointer;
    border: 0;
    border-radius: 20px;
    color: var(--lib-color-white, #fff);
    text-align: center;
    font-feature-settings: "clig" off, "liga" off;
    gap: 4px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;

    .tag-text {
      max-width: 5vw;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      align-items: center;
    }

    .no-text-ellipsis {
      max-width: 130px;
    }

    img {
      border-radius: 50%;
    }
    
    &.primary {
      justify-content: center;
      align-items: center;
      background: var(--lib-tag-primary-color, var(--lib-color-primary, #4940d7));

      &:hover {
        background: var(--lib-tag-primary-hover-color, var(--lib-color-purple-2, #352f9d));
      }

      &:active {
        background: var(--lib-tag-primary-active-color, var(--lib-color-tertiary, #1d1878));
      }

      &.disabled {
        background: var(--lib-color-gray-6, #e9e9e9);
        color: var(--lib-color-gray-4, #8d8d8d);
        pointer-events: none;
        cursor: not-allowed;
      }
    }

    &.secondary {
      justify-content: center;
      align-items: center;
      color: var(--lib-color-primary, #4940d7);
      border: 1px solid var(--lib-color-primary, #4940d7);
      background-color: var(--lib-color-white, #fff);

      &:hover {
        border: 1px solid var(--lib-color-tertiary, #1d1878);
        background-color: var(--lib-color-purple-6, #cfcdf4);
      }

      &:active {
        color: var(--lib-color-tertiary, #1d1878);
        border: 1px solid var(--lib-color-tertiary, #1d1878);
        background-color: var(--lib-color-secondary, #ecebfc);
      }

      &.disabled {
        border: 1px solid var( --lib-color-gray-4, #8d8d8d);
        background-color: var(--lib-color-white, #fff);
        color: var(---lib-color-gray-4, #8d8d8d);
        pointer-events: none;
        cursor: not-allowed;
      }
    }

    &.sm {
      font-size: $tag-sm;

      &.tagWithImg {
        padding: 2px 10px;
        .tagImg {
          width: 20px;
          height: 20px;
        }
      }

      &.label-only {
        padding: 8px 10px;
      }

      &.icon {
        i {
          font-size: $tag-sm;
        }
        padding: 6px 10px;
      }

      &.close-icon {
        i {
          font-size: $tag-sm;
        }
        padding: 6px 8px 6px 10px;
      }

      &.hyperlink {
        text-decoration: underline;
      }
    }

    &.md {
      font-size: $tag-md;

      &.tagWithImg {
        padding: 2px 10px;

        .tagImg {
          width: 24px;
          height: 24px;
        }
      }
      
      &.label-only {
        padding: 10px 12px;
      }

      &.icon {
        i {
          font-size: $tag-md;
        }
        padding: 6px 12px;
      }

      &.close-icon {
        i {
          font-size: $tag-md;
        }
        padding: 6px 10px 6px 12px;
      }

      &.hyperlink {
        text-decoration: underline;
      }
    }

    &.no-hyperlink {
      text-decoration: none;
    }
  }
}

// Tag style needed for autocomplete component
.activeTag {
  .tag {
    background: var(--lib-color-tertiary, #1d1878) !important;
  }
}
