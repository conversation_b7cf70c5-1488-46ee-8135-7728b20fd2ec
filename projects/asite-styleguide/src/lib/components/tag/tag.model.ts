export enum TagSizeEnum {
  Small = 'sm',
  Medium = 'md',
}

export type TagSize = TagSizeEnum.Small | TagSizeEnum.Medium;

export enum TagTypeEnum {
  Primary = 'primary',
  Secondary = 'secondary',
}

export type TagType = TagTypeEnum.Primary | TagTypeEnum.Secondary;

export enum TagLinkTypeEnum {
  Hyperlink = 'hyperlink',
  NoHyperlink = 'no-hyperlink',
}

export type TagLink = TagLinkTypeEnum;

/**
 * @description Tags
 * @export
 * @interface TagList
 */

export interface TagList
{
  Id:number,
  Label: string,
  Icon?: string,
  imgLink?: string,
}
