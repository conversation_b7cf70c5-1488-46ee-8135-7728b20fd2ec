import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TagComponent } from './tag.component';
import { TagSizeEnum, TagType, TagTypeEnum,TagSize, TagList, TagLinkTypeEnum } from './tag.model';

describe('TagComponent', () => {
  let component: TagComponent;
  let fixture: ComponentFixture<TagComponent>;

  beforeEach(async () => {
    fixture = TestBed.createComponent(TagComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

describe('Tag Types Enums', () => {
  it('should have correct enum values', () => {
    expect(TagTypeEnum.Primary).toEqual('primary');
    expect(TagTypeEnum.Secondary).toEqual('secondary');
  });
});

describe('Tag Type Input', () => {
  it('should allow Primary tag type', () => {
    const tagType: TagType = TagTypeEnum.Primary;
    expect(tagType).toEqual(TagTypeEnum.Primary);
  });

  it('should allow Secondary tag type', () => {
    const tagType: TagType = TagTypeEnum.Secondary;
    expect(tagType).toEqual(TagTypeEnum.Secondary);
  });
});

describe('Tag Sizes Enums', () => {
  it('should have correct enum values', () => {
    expect(TagSizeEnum.Small).toEqual('sm');
    expect(TagSizeEnum.Medium).toEqual('md');
  });
});

describe('Tag Size Input', () => {
  it('should allow small Tag size', () => {
    const size: TagSize = TagSizeEnum.Small;
    expect(size).toEqual(TagSizeEnum.Small);
  });

  it('should allow medium Tag size', () => {
    const size: TagSize = TagSizeEnum.Medium;
    expect(size).toEqual(TagSizeEnum.Medium);
  });
});

describe('TagList Input', () => {
  let component: TagComponent;
  beforeEach(() => {
    component = new TagComponent();
  });

  it('should not empty the tag list', () =>
  {
    component.tagList = [{
      Id: 1,
      Label: "Tag Example",
      Icon: ''
    },
    {
      Id: 2,
      Label: "Tag Large Text Example",
      Icon: ''
    }];

    expect(component.tagList).not.toBeNull();
  });


  it('should be according tagList interface', () =>
  {
    const mockTagList : TagList[] = [
      { Id: 1, Label: 'Tag Example', Icon: '' },
      { Id: 2, Label: 'Tag Large Text Example', Icon: '' },
    ];
    component.tagList = mockTagList;
    expect(component.tagList).toEqual(jasmine.any(Array));
    expect(component.tagList.every((tag) => typeof tag === 'object')).toBeTrue();
    expect(component.tagList.every((tag) => 'Id' in tag && 'Label' in tag && 'Icon' in tag)).toBeTrue();
  });

  it('should have tagList of type Tag[] with Icon and non-empty Label', () => {
    const sampleTagListWithIcon: TagList[] = [
      { Id: 1, Label: 'Tag Example', Icon: 'iconoir-xmark' },
      { Id: 2, Label: 'Tag Large Text Example', Icon: 'iconoir-xmark' },
    ];

    const sampleTagListWithoutIcon: TagList[] = [
      { Id: 1, Label: 'Tag Example', Icon: '' },
      { Id: 2, Label: 'Tag Large Text Example', Icon: '' },
    ];

    const sampleTagListWithEmptyLabel: TagList[] = [
      { Id: 1, Label: '', Icon: 'icon1' },
      { Id: 2, Label: 'Tag Large Text Example', Icon: 'icon2' },
    ];

    component.tagList = sampleTagListWithIcon;

    expect(component.tagList).toEqual(jasmine.any(Array));
    expect(component.tagList.every((tag) => typeof tag === 'object')).toBeTrue();
    expect(component.tagList.every((tag) => 'Id' in tag && 'Label' in tag && 'Icon' in tag)).toBeTrue();

    // Check whether Icon and Label property exists for each tag
    expect(component.tagList.every((tag) => tag.Icon !== undefined && tag.Icon !== null)).toBeTrue();

    expect(component.tagList.every((tag) => typeof tag.Label === 'string' && tag.Label.trim() !== '')).toBeTrue();
    component.tagList = sampleTagListWithoutIcon;
    expect(component.tagList.every((tag) => tag.Icon === '')).toBeTrue();

    component.tagList = sampleTagListWithEmptyLabel;
    expect(component.tagList.every((tag) => tag.Label === '')).toBeFalse();
  });

});

describe('Tag Disabled Input', () => {
  let component: TagComponent;
  beforeEach(() => {
    component = new TagComponent();
  });

  it('should have default value of false for disabled property', () => {
    expect(component.disabled).toBe(false);
  });

  it('should set disabled property to true', () => {
    component.disabled = true;
    expect(component.disabled).toBe(true);
  });

  it('should handle setting disabled property to false', () => {
    component.disabled = true;
    component.disabled = false;
    expect(component.disabled).toBe(false);
  });
});

describe('Tag closeIcon Input', () => {
  let component: TagComponent;
  beforeEach(() => {
    component = new TagComponent();
  });

  it('should have default value of false for isClosable property', () =>
  {
    component.isClosable = true;
    expect(component.isClosable).toBe(true);
  });
});

describe('Tag hyperLink Input', () => {
  let component: TagComponent;
  beforeEach(() => {
    component = new TagComponent();
  });

  it('should have default value of null for hyperLink property', () => {
    expect(component.hyperLink).toBeNull();
  });

  it('should set hyperLink property to Hyperlink', () => {
    component.hyperLink = TagLinkTypeEnum.Hyperlink;
    expect(component.hyperLink).toBe(TagLinkTypeEnum.Hyperlink);
  });
  it('should set hyperLink property to No Hyperlink', () => {
    component.hyperLink = TagLinkTypeEnum.NoHyperlink;
    expect(component.hyperLink).toBe(TagLinkTypeEnum.NoHyperlink);
  });
});

describe('Tag onClick Output', () => {
  let component: TagComponent;
  let fixture: ComponentFixture<TagComponent>;
  beforeEach(() => {
    fixture = TestBed.createComponent(TagComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should emit an event when clicked', () => {
    const component = new TagComponent();
    spyOn(component.onClick, 'emit');
    const event = new Event('click');
    component.onClick.emit(event);
    expect(component.onClick.emit).toHaveBeenCalledWith(event);
  });

});
