<ng-container *ngIf="tagList">
  <div class="tag-container" [ngClass]="{ 'activeTag': activeTag }">
  <div *ngFor="let tag of tagList" class="tag" [ngClass]="[
    type || '',
    size || '',
    hyperLink || '',
    tag.Icon && !tagWithImg && !isClosable ? 'icon' : 
      tagWithImg ? 'tagWithImg' : isClosable ? 'close-icon' : 'label-only'
  ]"
    [class.disabled]="disabled">
      <i [class]="tag.Icon" *ngIf="tag.Icon && !tagWithImg" role="img" aria-label="icon"></i>
      <img *ngIf="tagWithImg" [src]="tag['imgLink']" [alt]="tag['Label']" class="tagImg" />
      <span class="tag-text" [class]="hyperLink" [ngClass]="{ 'no-text-ellipsis': !textEllipsis }" [title]="tag['Label']">{{tag.Label}}</span>
      <i class="iconoir-xmark" *ngIf="isClosable" (click)="$event['tag'] = tag; onClick.emit($event)" role="img" aria-label="close Icon"></i>
    </div>
  </div>
</ng-container>