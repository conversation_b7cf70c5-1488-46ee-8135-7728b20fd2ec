import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { TagSize, TagSizeEnum, TagType, TagTypeEnum, TagLink, TagList } from './tag.model';
import { CommonModule } from '@angular/common';

@Component({
  standalone: true,
  imports: [CommonModule],
  selector: 'lib-tag',
  templateUrl: './tag.component.html',
  styleUrls: ['./tag.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TagComponent
{
  /**
   * Specify the size of the tag
  */
  @Input() type: TagType = TagTypeEnum.Primary;

  @Input() size: TagSize = TagSizeEnum.Medium;

  @Input() tagList: TagList[] = [];

  @Input() disabled: boolean = false;

  @Input() isClosable: boolean = false;

  @Input() hyperLink: TagLink | null = null;

  // Input properties needed for autocomplete component
  @Input() activeTag: boolean = false;

  @Input() textEllipsis: boolean = true;

  @Input() tagWithImg: boolean = false;
  
  /**
   * click handler
   */
  @Output()
  onClick = new EventEmitter<Event>();
}
