import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ButtonGroupComponent } from './button-group.component';
import { ButtonGroupSize, ButtonGroupSizeEnum, buttonGroupItem } from './button-group.model';

describe('ButtonGroupComponent', () => {
  let component: ButtonGroupComponent;
  let fixture: ComponentFixture<ButtonGroupComponent>;

  beforeEach(async () => {

    fixture = TestBed.createComponent(ButtonGroupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default Button Group data as an empty array', () => {
    expect(component.buttonGroupData).toEqual([]);
  });

  it('should have default first Button selected', () => {
    expect(component.selectedButtonGroup).toBeUndefined();
  });

  it('should set selectedButtonGroup', () => {
    const selectedButtonGroup = 1;
    component.selectedButtonGroup = selectedButtonGroup;
    expect(component.selectedButtonGroup).toEqual(selectedButtonGroup);
  });

  it('should emit selectButton event when a Button from button-group is selected', () => {
    const mockButtonGroup: buttonGroupItem = {
      id: 1,
      label: 'Button 1',
      icon: 'iconoir-lens',
      disabled: false,
    };
    spyOn(component.buttomSeleted, 'emit');

    component.selectButton(mockButtonGroup);

    expect(component.buttomSeleted.emit).toHaveBeenCalledWith(mockButtonGroup);
  });

  it('should accept different buttonGroupData through input', () => {
    const expectedMenuItems: buttonGroupItem[] = [
      { id: 1, label: 'Button 1', icon: 'iconoir-lens', disabled: false },
      { id: 2, label: 'Button 2', icon: 'iconoir-lens', disabled: false },
      { id: 3, label: 'Button 3', icon: 'iconoir-lens', disabled: false },
      { id: 4, label: 'Button 4', icon: 'iconoir-lens', disabled: false },
    ];
    component.buttonGroupData = expectedMenuItems;
    fixture.detectChanges();
    expect(component.buttonGroupData).toEqual(expectedMenuItems);
  });
});

describe('Button Sizes Enums', () => {
  it('should have correct enum values', () => {
    expect(ButtonGroupSizeEnum.Small).toEqual('sm');
    expect(ButtonGroupSizeEnum.Medium).toEqual('md');
    expect(ButtonGroupSizeEnum.Large).toEqual('lg');
  });
});

describe('Button Size Input', () => {
  it('should allow small button size', () => {
    const size: ButtonGroupSize = ButtonGroupSizeEnum.Small;
    expect(size).toEqual(ButtonGroupSizeEnum.Small);
  });

  it('should allow medium button size', () => {
    const size: ButtonGroupSize = ButtonGroupSizeEnum.Medium;
    expect(size).toEqual(ButtonGroupSizeEnum.Medium);
  });

  it('should allow large button size', () => {
    const size: ButtonGroupSize = ButtonGroupSizeEnum.Large;
    expect(size).toEqual(ButtonGroupSizeEnum.Large);
  });
});
