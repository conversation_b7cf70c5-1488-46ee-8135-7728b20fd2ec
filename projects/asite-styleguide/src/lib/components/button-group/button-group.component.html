<div class="main" *ngIf="buttonGroupData">
  <ng-container
    *ngFor="let button of buttonGroupData; let i = 'index+1';"
  >
    <button
      type="button"
      class="btn-group"
      [attr.aria-label]="!button.label ? button.title || button.label : ''"
      [attr.title]="!button.label ? button.title || button.label : ''"
      (click)="selectButton(button)"
      [ngClass]="[
        !button.disabled ? !selectedButtonGroup && selectedButtonGroup != 0 && i == 1 ? 'selected' : selectedButtonGroup == i ? 'selected' : 'default' : 'default',
        size,
        button.label && button.icon ? 'withIcon' : button.label && !button.icon ? 'labelOnly' : 'iconOnly', 
        i == 1 ? 'left' : i == buttonGroupData.length ? 'right' : 'center'
      ]"
      [class.disabled]="button.disabled"
    >
      <i [class]="button.icon" *ngIf="button.icon"></i>
      <span class="label" *ngIf="button.label">{{ button.label }}</span>
    </button>
  </ng-container>
</div>
