import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import {
  ButtonGroupSize,
  ButtonGroupSizeEnum,
  buttonGroupItem,
} from '../index';
import { CommonModule } from '@angular/common';

@Component({
  standalone: true,
  imports: [CommonModule],
  selector: 'lib-button-group',
  templateUrl: './button-group.component.html',
  styleUrls: ['./button-group.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ButtonGroupComponent {
  /**
   * @description Array containing Button Group Item.
   * @memberof ButtonGroupComponent
   */
  @Input() buttonGroupData: buttonGroupItem[] = [];

  /**
   * @description Specify the which default button from Button group should be selected.
   */
  @Input() selectedButtonGroup : number;

  /**
   * @description Specify the size of the Button group
   */
  @Input() size: ButtonGroupSize = ButtonGroupSizeEnum.Medium;

  /**
   * @description return the selected button
   */
  selectButton(button) {
    if (!button.disabled) {
      this.selectedButtonGroup = button.id;
      this.buttomSeleted.emit(button);
    }
  }

  @Output() buttomSeleted = new EventEmitter<buttonGroupItem>();
}
