@import '../../../../assets/scss/styles.scss';

// Button Size
$btn-sm: 0.875rem;
$btn-md: 1rem;
$btn-lg: 1.125rem;

.main {
    display: inline-flex;
    align-items: flex-start;
    border-radius: 8px 0px 0px 8px;

    .btn-group {

        font-family: $fontFamily;
        background: var(--lib-color-gray-7, #FAFAFA);
        color: var(--lib-color-tertiary, #1D1878);
        border: none;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        gap: 6px;
        text-align: center;
        font-feature-settings: 'clig' off, 'liga' off;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        text-wrap: nowrap;

        &.center {
            border-right: 0.5px solid var(--lib-color-tertiary, #1D1878);
            border-left: 0.5px solid var(--lib-color-tertiary, #1D1878);
        }

        &.left {
            border-radius: 8px 0px 0px 8px;
            border-right: 0.5px solid var(--lib-color-tertiary, #1D1878);
        }

        &.right {
            border-radius: 0px 8px 8px 0px;
            border-left: 0.5px solid var(--lib-color-tertiary, #1D1878);
        }

        &.default {

            &:hover,&:focus-visible {
                color: var(--lib-color-primary, #4940D7);
                background: var(--lib-color-gray-6, #E9E9E9);
            }

            &:active {
                color: var(--lib-color-tertiary, #1D1878);
                background: var(--lib-color-gray-6, #E9E9E9);
            }

            &.disabled {
                color: var(--lib-color-gray-5, #C5C5C5);
                background: var(--lib-color-white, #FFF);
                border-color: var(--lib-color-gray-5, #C5C5C5);
                pointer-events: none;
                cursor: not-allowed;
            }
        }

        &.selected {

            background: var(--lib-color-secondary, #ECEBFC);
            color: var(--lib-color-tertiary, #1D1878);

            &:hover,&:focus-visible {
                color: var(--lib-color-tertiary, #1D1878);
                background: var(--lib-color-purple-6, #CFCDF4);
            }

            &:active {
                color: var(--lib-color-black, #0C1315);
                background: var(--lib-color-purple-5, #9892F2);
            }

            &.disabled {
                color: var(--lib-color-gray-5, #C5C5C5);
                background: var(--lib-color-gray-6, #E9E9E9);
                border-color: var(--lib-color-gray-5, #C5C5C5);
                pointer-events: none;
                cursor: not-allowed;
            }
        }

        &.sm {
            font-size: $btn-sm;

            &.labelOnly {
                padding: 11px 12px;
            }

            &.withIcon {
                i {
                    font-size: 1.125rem;
                }

                padding: 7px 12px;
            }

            &.iconOnly {
                font-size: 1.125rem;
                padding: 7px;
            }
        }

        &.md {
            font-size: $btn-md;

            &.labelOnly {
                padding: 16px 14px;
            }

            &.withIcon {
                i {
                    font-size: 1.25rem;
                }

                padding: 11px 14px;
            }

            &.iconOnly {
                font-size: 1.25rem;
                padding: 11px;
            }
        }

        &.lg {
            font-size: $btn-lg;

            &.labelOnly {
                padding: 20px 18px;
            }

            &.withIcon {
                i {
                    font-size: 1.5rem;
                }

                padding: 14px 16px;
            }

            &.iconOnly {
                font-size: 1.5rem;
                padding: 14px;
            }
        }



    }
}