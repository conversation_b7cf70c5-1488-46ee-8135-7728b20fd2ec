import { A11yModule } from '@angular/cdk/a11y';
import { Component, ContentChild, Input, Output, TemplateRef,EventEmitter, HostListener, ViewChild, ElementRef, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  standalone: true,
  imports:[CommonModule, A11yModule],
  selector: 'lib-dialog-box',
  templateUrl: './dialog-box.component.html',
  styleUrls: ['./dialog-box.component.scss']
})
export class DialogBoxComponent {

  @Input() dialogTitle: string = "";

  @Input() isDialogOpen: boolean = false;

  /**
   * If true, disables animations for the dialog box.
   * Set to false to enable animations.
   */
  @Input() noAnimation: boolean = false; 

  @ContentChild('headerTemplate') headerTemplate: TemplateRef<any> | null = null;

  @ContentChild('contentTemplate') contentTemplate: TemplateRef<any> | null = null;

  @ContentChild('footerTemplate') footerTemplate: TemplateRef<any> | null = null;

  /**
   * close click handler
   */
  @Output() dialogCloseEvent: EventEmitter<Event> = new EventEmitter<Event>();


  public currentZIndex: number = 1;

  /* Open Dialog on Click */
  openDialogBox(): void {
    this.isDialogOpen = true;
  }

  /* Close Dialog on Click */
  closeDialogBox(event:Event): void {
    this.isDialogOpen = false;
    this.dialogCloseEvent.emit(event);
  }

  /* Close Dialog on Esc Press */
  @HostListener('document:keydown.escape', ['$event'])
  onCloseEsc(event: KeyboardEvent): void {
    if (this.isDialogOpen && (event.key === 'Escape' || event.key === 'Esc')) {
      event.preventDefault();
      this.closeDialogBox(event);
    }
  }

   /* Assign a unique z-index based on the number of dialogs */
   ngOnChanges(changes: SimpleChanges): void {
    if (changes['isDialogOpen'] && this.isDialogOpen && !changes['isDialogOpen'].previousValue) {
      this.currentZIndex = this.getMaxZIndex() + 1;
    }
  }

  /* get Max z-index from dialogs */
  private getMaxZIndex(): number {
    const elements = Array.from(document.querySelectorAll<HTMLElement>('.dialogbox'));
    const zIndices = elements.map((el) => parseInt(window.getComputedStyle(el).zIndex, 10) || 0);
    return zIndices.length ? Math.max(...zIndices) : 950;
  }
}
