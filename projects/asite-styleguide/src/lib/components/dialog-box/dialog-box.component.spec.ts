import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DialogBoxComponent } from './dialog-box.component';
import { SimpleChange, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';

describe('DialogBoxComponent', () => {
  let component: DialogBoxComponent;
  let fixture: ComponentFixture<DialogBoxComponent>;

  beforeEach(async () => {
    fixture = TestBed.createComponent(DialogBoxComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});


describe('openDialogBox Method', () => {
  let component: DialogBoxComponent;
  let fixture: ComponentFixture<DialogBoxComponent>;

  beforeEach(async () => {
    fixture = TestBed.createComponent(DialogBoxComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });
  it('should open the dialog box', () => {
    expect(component.isDialogOpen).toBeFalsy();
    component.openDialogBox();
    expect(component.isDialogOpen).toBeTruthy();
  });

});

describe('closeDialogbox Method', () => {
  let component: DialogBoxComponent;
  let fixture: ComponentFixture<DialogBoxComponent>;

  beforeEach(async () => {
    fixture = TestBed.createComponent(DialogBoxComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should set isDialogOpen to false when called', function() {
    const component = new DialogBoxComponent();
    component.isDialogOpen = true;
    component.closeDialogBox(new Event('click'));
    expect(component.isDialogOpen).toBe(false);
  });

  it('should not change isDialogOpen when it is already false', function() {
    const component = new DialogBoxComponent();
    component.isDialogOpen = false;
    component.closeDialogBox(new Event('click'));
    expect(component.isDialogOpen).toBe(false);
  });

  it('should emit dialogCloseEvent when called', function() {
    const component = new DialogBoxComponent();
    const event = new Event('click');
    spyOn(component.dialogCloseEvent, 'emit');
    component.closeDialogBox(event);
    expect(component.dialogCloseEvent.emit).toHaveBeenCalledWith(event);
  });

});

describe('onCloseEsc Method', () => {
  let component: DialogBoxComponent;
  let fixture: ComponentFixture<DialogBoxComponent>;

  beforeEach(async () => {
    fixture = TestBed.createComponent(DialogBoxComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });


  it('should close dialog box when Escape key is pressed and dialog is open', () => {
    const component = new DialogBoxComponent();
    component.isDialogOpen = true;
    const event = new KeyboardEvent('keydown', { key: 'Escape' });
    component.onCloseEsc(event);
    expect(component.isDialogOpen).toBe(false);
  });

  it('should not close dialog box when different key than Escape is pressed and dialog is open', () => {
    const component = new DialogBoxComponent();
    component.isDialogOpen = true;
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    component.onCloseEsc(event);
    expect(component.isDialogOpen).toBe(true);
  });

  it('should not prevent default behavior of different key than Escape when dialog is open', () => {
    const component = new DialogBoxComponent();
    component.isDialogOpen = true;
    const event = new KeyboardEvent('keydown', { key: 'Enter' });
    component.onCloseEsc(event);
    expect(event.defaultPrevented).toBe(false);
  });
});

describe('DialogBoxComponent - ngOnChanges', () => {
  let component: DialogBoxComponent;

  beforeEach(() => {
    component = new DialogBoxComponent();
    spyOn<any>(component, 'getMaxZIndex').and.returnValue(1000);
  });

  it('should set currentZIndex to max z-index + 1 when isDialogOpen changes to true', () => {
    component.isDialogOpen = true;

    const changes: SimpleChanges = {
      isDialogOpen: new SimpleChange(false, true, false)
    };

    component.ngOnChanges(changes);

    expect((component as any).getMaxZIndex).toHaveBeenCalled();
    expect(component.currentZIndex).toBe(1001);
  });

  it('should not update currentZIndex if previousValue is true', () => {
    component.isDialogOpen = true;

    const changes: SimpleChanges = {
      isDialogOpen: new SimpleChange(true, true, false)
    };

    component.ngOnChanges(changes);

    expect((component as any).getMaxZIndex).not.toHaveBeenCalled();
  });
});

describe('DialogBoxComponent - getMaxZIndex', () => {
  let component: DialogBoxComponent;

  beforeEach(() => {
    component = new DialogBoxComponent();
  });

  function createMockElement(zIndex: string): HTMLElement {
    const el = document.createElement('div');
    el.style.zIndex = zIndex;
    return el;
  }

  it('should return the max z-index from elements', () => {
    const mockElements = [
      createMockElement('1000'),
      createMockElement('1050'),
      createMockElement('1020')
    ];

    spyOn(document, 'querySelectorAll').and.returnValue(mockElements as any);

    expect((component as any).getMaxZIndex()).toBe(0);
  });

  it('should return 950 if no elements are found', () => {
    spyOn(document, 'querySelectorAll').and.returnValue([] as any);

    expect((component as any).getMaxZIndex()).toBe(950);
  });

  it('should handle invalid z-index values gracefully', () => {
    const mockElements = [
      createMockElement('invalid'),
      createMockElement('auto'),
      createMockElement('')
    ];

    spyOn(document, 'querySelectorAll').and.returnValue(mockElements as any);

    expect((component as any).getMaxZIndex()).toBe(0);
  });
});

