<ng-container *ngIf="isDialogOpen">
  <div 
    cdkTrapFocus [cdkTrapFocusAutoCapture]="false"
    class="dialogbox"
    role="dialog"
    aria-modal="false" 
    tabindex="-1"
    aria-labelledby="dialogbox-title"
    aria-describedby="dialogbox-content"
    (keyup)="onCloseEsc($event)" 
    (closeDialog)="closeDialogBox()"
    [ngClass]="[isDialogOpen ? 'open' : 'closed', noAnimation ? '' : 'dialogbox-animation']" [style.zIndex]="currentZIndex">
    <div class="dialogbox-header">
      <span *ngIf="dialogTitle" id="dialogbox-title" class="dialogbox-title title-medium" aria-labelledby="{{ dialogTitle }}">{{ dialogTitle }}</span>
      <ng-container *ngTemplateOutlet="headerTemplate"></ng-container>
    </div>
    <div class="dialogbox-content" id="dialogbox-content">
      <ng-container *ngTemplateOutlet="contentTemplate"></ng-container>
    </div>
    <div class="dialogbox-footer" *ngIf="footerTemplate">
      <div class="action-buttons"><ng-container *ngTemplateOutlet="footerTemplate"></ng-container></div>
    </div>
  </div>
  <div class="dialogbox-overlay" aria-hidden="true" [style.zIndex]="+currentZIndex - 1"></div>
</ng-container>
