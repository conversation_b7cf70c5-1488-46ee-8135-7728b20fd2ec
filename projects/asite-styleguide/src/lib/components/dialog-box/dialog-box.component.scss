@import '../../../../assets/scss/styles.scss';

/* Dailog Component Style */
.dialogbox {
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 16px;
  border: 1px solid var(--lib-color-gray-6, #E9E9E9);
  background: var(--lib-color-white, #ffffff);
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
  flex-direction: column;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-family: $fontFamily;
  z-index: 6;
  padding: 0;
  min-width: 26vw;

  /* Animation on Dialog */
  &.dialogbox-animation {
    animation: dialogAnimation 300ms linear;
  }

  @keyframes dialogAnimation {
    0% {
      transform: translate(-50%, -50%) scale(0.6);
    }

    50% {
      transform: translate(-50%, -50%) scale(1);
    }

    100% {
      transform: translate(-50%, -50%) scale(1);
    }
  }

  .dialogbox-header {
    display: flex;
    justify-content: center;
    border-bottom: 1px solid var(--lib-color-gray-6, #E9E9E9);
    align-self: stretch;

    .dialogbox-title {
      color: var(--lib-color-black, #0C1315);
      text-align: center;
      padding: 20px 30px;
    }
  }

  .dialogbox-content {
    display: flex;
    flex-direction: column;
    padding: 16px 24px;
    overflow: auto;
    scrollbar-width: none;
    width: 100%;
    max-height: calc(100vh - 250px);
    box-sizing: border-box;
  }

  .dialogbox-footer {
    display: flex;
    align-self: stretch;
    justify-content: flex-end;
    gap: 10px;
    border-top: 1px solid var(--lib-color-gray-6, #E9E9E9);

    .action-buttons{
      display: flex;
      gap: 10px;
      padding: 16px 25px;
    }
  }
}

.dialogbox-overlay {
  position: fixed;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  opacity: 0.5;
  background: var(--lib-color-black, #0C1315);
  z-index: 5;
}
