@import "../../../../assets/scss/styles.scss";

*{
  box-sizing: border-box;
}
.data-source-table {
  margin: 19px;
  border-radius: 12px;
  font-family: $fontFamily;
  border: 1px solid var(--grey-lighten-40, #e9e9e9);
  background: var(--Neutral-White, #fff);
  min-width: 550px;

  table {
    text-align: left;
    border-collapse: collapse;
    width: 100%;
    padding: 0.3rem;
    table-layout: fixed;

    tr:hover td {
      border-bottom: 1px solid var(--grey-lighten-30, #c5c5c5);
      background: var(--Product-Secondary---Lavender, #ecebfc);
    }

    th,
    td {
      padding: 19px 8px 20px 8px;
      word-wrap: break-word;
      word-break: break-word;
      font-size: 14px;
      font-style: normal;
      line-height: normal;
    }
    td:nth-child(1),
    th:nth-child(1) {
      padding: 19px 8px 20px 16px;
    }

    th {
      color: var(--Product-Tertiary---Purple, #1d1878);
      border-radius: 12px 12px 0px 0px;
      border-bottom: 2px solid var(--grey-lighten-30, #c5c5c5);
      background: var(--grey-lighten-50, #fafafa);
      text-transform: capitalize;
      font-weight: 600;
    }

    td {
      border-bottom: 1px solid var(--grey-lighten-30, #c5c5c5);
      background: var(--Neutral-White, #fff);
      font-weight: 400;
    }
  }
}

.pagination {
  display: flex;
  gap: 16px;
  height: 50px;
  padding: 0px 12px;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  border-radius: 0px 0px 12px 12px;
  background: var(--Neutral-White, #fff);
  box-shadow: 0px 1px 0px 0px #bdbdbd inset;

  .total-records {
    color: var(--Neutral-Grey, #616161);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .pages,
  .records {
    display: flex;
    align-items: center;
    position: relative;
    p {
      color: var(--Neutral-Grey, #616161);
      font-size: 10px;
      font-style: normal;
      font-weight: 400;
      line-height: 14px;
    }

    .iconoir-nav-arrow-down {
      font-size: 20px;
    }

    .dropdown-dd {
      position: absolute;
      bottom: 38px;
      left: 0px;
      display: flex;
      flex-direction: column;
      width: 110px;
      max-height: 148px;
      padding: 8px 6px;
      align-items: flex-start;
      gap: 10px;
      flex-shrink: 0;
      border-radius: 12px;
      filter: drop-shadow(0px 2px 2px rgba(0, 0, 0, 0.25));
      border: 1px solid var(--grey-lighten-40, #e9e9e9);
      background: var(--Neutral-White, #fff);
      -ms-overflow-style: none; /* for Internet Explorer, Edge */
      scrollbar-width: none; /* for Firefox */
      overflow-y: scroll;

      &::-webkit-scrollbar {
        display: none; /* for Chrome, Safari, and Opera */
      }

      .dd-option {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 8px;
        width: 100%;
        padding: 10px;
        cursor: pointer;

        i.iconoir-check {
          font-size: 21px;
          color: var(--Product-Tertiary---Purple, #1d1878);
        }

        &:hover {
          border-radius: 10px;
          background: var(--Product-Secondary---Lavender, #ecebfc);
        }
      }
      .dd-option-colour {
        border-radius: 10px;
        background: var(--Product-Secondary---Lavender, #ecebfc);
      }
    }
  }

  .pages {
    gap: 16px;

    .page-list {
      left: -15px;
    }

    .page-filter {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 5px;

      p:nth-child(1) {
        color: var(--Neutral-Black, #0c1315);
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
    }
  }
  .records {
    gap: 4px;

    .itemPerPageNo {
      color: var(--Neutral-Black, #0c1315);
      margin-left: 6px;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
  span {
    cursor: pointer;

    i {
      font-size: 24px;
      display: block;
    }
  }
  .disable-arrow {
    pointer-events: none;
    opacity: 0.7;
  }
}
