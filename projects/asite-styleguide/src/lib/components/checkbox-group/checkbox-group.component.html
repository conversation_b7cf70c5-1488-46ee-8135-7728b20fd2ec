<div class="checkbox-wrapper" *ngIf="checkboxGroupData">
  <p *ngIf="label">{{label}} <span *ngIf="isRequired || false" style="color:red">&#42;</span></p>
  <div [class]="orientation">
    <ng-container *ngFor="let chkData of checkboxGroupData; let i = 'index+1';">
      <div class="input-wrapper">
        <input [(ngModel)]="chkData.isChecked" (change)="onChange(chkData, $event.target.checked)" [name]="chkData.name"
          type="checkbox" [id]="chkData.id" [disabled]="chkData.disabled">
        <i [class]="chkData.icon" [ngClass]="{'disable': chkData.disabled}" aria-hidden="true" *ngIf="chkData.icon"></i>
        <label [ngClass]="{'disable': chkData.disabled}" [for]="chkData.id" *ngIf="chkData.title">{{chkData.title}}</label>
      </div>
    </ng-container>
  </div>
</div>
