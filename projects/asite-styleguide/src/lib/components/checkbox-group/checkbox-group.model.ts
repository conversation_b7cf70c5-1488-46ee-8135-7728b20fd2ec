export enum OrientationEnum {
  Horizontal = 'horizontal',
  Vertical = 'vertical',
}

export type OrientationPosition = OrientationEnum.Horizontal | OrientationEnum.Vertical;

/**
 * @description checkbox group
 * @export
 * @interface checkboxGroupItem
 */
export interface checkboxGroupItem {
  id: number;
  title: string;
  icon?: string;
  name: string;
  disabled?: boolean;
  checked?: boolean;
  isRequired?: boolean;
}
