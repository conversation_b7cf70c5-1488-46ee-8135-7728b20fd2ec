import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { OrientationEnum, OrientationPosition, checkboxGroupItem } from './checkbox-group.model';

@Component({
  selector: 'lib-checkbox-group',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './checkbox-group.component.html',
  styleUrl: './checkbox-group.component.scss'
})
export class CheckboxGroupComponent {
  /**
    * @description Array containing Button Group Item.
    * @memberof CheckboxGroupComponent
    */
  @Input() checkboxGroupData: checkboxGroupItem[] = [];

  /**
    * @description Specify the orientation of the Checkbox group
    */
  @Input() orientation: OrientationPosition = OrientationEnum.Horizontal;

  /**
    * @description label of the Checkbox group
    */
  @Input() label: string = '';

  @Output() checkboxSelected = new EventEmitter<any>();

  /**
   * @description emit the selected checkbox
   */
  onChange(checkboxData, ischecked) {
    if (!checkboxData.disabled) {
      checkboxData.checked = ischecked;
      this.checkboxSelected.emit(this.checkboxGroupData);
    }
  }
}
