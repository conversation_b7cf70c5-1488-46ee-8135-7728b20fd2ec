import { TestBed, ComponentFixture } from '@angular/core/testing';
import { CheckboxGroupComponent } from './checkbox-group.component';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { By } from '@angular/platform-browser';

describe('CheckboxGroupComponent', () => {
  let component: CheckboxGroupComponent;
  let fixture: ComponentFixture<CheckboxGroupComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CommonModule, FormsModule]
    })
      .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CheckboxGroupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should update checkboxData.checked and emit checkboxSelected event if checkboxData is not disabled', () => {
    // Setup
    const checkboxData = {
      checked: false,
      disabled: false,
      label: 'Checkbox 1',
      value: 'checkbox1',
      id: 1,
      name: "checkbox",
      title: "checkbox"
    };
    spyOn(component.checkboxSelected, 'emit');

    // Execution
    component.onChange(checkboxData, true);

    // Assertion
    expect(checkboxData.checked).toBe(true);
    expect(component.checkboxSelected.emit).toHaveBeenCalledWith(component.checkboxGroupData);
  });

  it('should not update checkboxData.checked or emit checkboxSelected event if checkboxData is disabled', () => {
    // Setup
    const checkboxData = {
      checked: false,
      label: 'Checkbox 1',
      value: 'checkbox1',
      id: 1,
      name: "checkbox",
      title: "checkbox",
      disabled: true
    };
    spyOn(component.checkboxSelected, 'emit');

    // Execution
    component.onChange(checkboxData, true);

    // Assertion
    expect(checkboxData.checked).toBe(false); // Should remain unchanged
    expect(component.checkboxSelected.emit).not.toHaveBeenCalled();
  });
});
