@import "../../../../assets/scss/styles.scss";

.checkbox-wrapper {
  font-family: $fontFamily;
  font-style: normal;
  line-height: normal;

  &>p {
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    color: var(--lib-color-black, #0c1315);
    margin-bottom: 20px;
  }

  .vertical {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .horizontal {
    display: flex;
    flex-direction: row;
    gap: 25px;
  }

  .input-wrapper {
    display: flex;
    align-items: center;

    input[type='checkbox'] {
      position: relative;
      border: 2px solid var(--Product-Primary---Lilac, #4940D7);
      border-radius: 6px;
      background: none;
      cursor: pointer;
      line-height: 0;
      margin: 0 0.6em 0 0;
      outline: 0;
      padding: 0 !important;
      vertical-align: text-top;
      height: 18px;
      width: 18px;
      -webkit-appearance: none;
      opacity: 1;

      &:checked {
        background-color: var(--lib-color-primary, #4940d7);

        &:hover {
          background: var(--lib-color-purple-3, #3d36b4);
        }

        &:active {
          background: var(--lib-color-purple-2, #352f9d);
        }

        &:disabled {
          background-color: var(--lib-color-gray-4, #8D8D8D);
          border-color: var(--lib-color-gray-4, #8d8d8d);
        }
      }

      &:disabled {
        border-color: var(--lib-color-gray-4, #8d8d8d);
      }

      &:disabled:before {
        border: none;
      }

      &:checked:disabled:before {
        border: solid var(--Neutral-White, #FFF);
        border-width: 0 2px 2px 0;
      }
    }

    input[type='checkbox']:before {
      content: '';
      position: absolute;
      right: 50%;
      top: 50%;
      width: 4px;
      height: 10px;
      border: solid var(--Neutral-White, #FFF);
      border-width: 0 2px 2px 0;
      margin: -1px -1px 0 -1px;
      transform: rotate(45deg) translate(-50%, -50%);
    }
    i {
      font-size: 1.25rem;
      margin-right: 10px;
    }
    .disable {
      color: var(--lib-color-gray-4,#8D8D8D);
    }
    label{
      cursor: pointer;
    }
  }
}
