import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ProgressComponent } from './progress.component';
import { ProgressSizeEnum, ProgressTypeEnum, ProgressSize, ProgressType } from './progress.model';

describe('ProgressComponent', () => {
  let component: ProgressComponent;
  let fixture: ComponentFixture<ProgressComponent>;

  beforeEach(async () => {
    fixture = TestBed.createComponent(ProgressComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default values', () => {
    expect(component.label).toEqual('');
    expect(component.size).toEqual(ProgressSizeEnum.Medium);
    expect(component.type).toEqual('');
    expect(component.isError).toEqual(false);
    expect(component.percentage).toEqual(0);
  });
});

describe('Progress Types Enums', () => {
  it('should have correct enum values', () => {
    expect(ProgressTypeEnum.Bar).toEqual('bar');
    expect(ProgressTypeEnum.Ring).toEqual('ring');
  });
});

describe('Progress Type Input', () => {
  it('should allow Bar tag type', () => {
    const ProgressType: ProgressType = ProgressTypeEnum.Bar;
    expect(ProgressType).toEqual(ProgressTypeEnum.Bar);
  });

  it('should allow Ring tag type', () => {
    const ProgressType: ProgressType = ProgressTypeEnum.Ring;
    expect(ProgressType).toEqual(ProgressTypeEnum.Ring);
  });
});

describe('Progress Sizes Enums', () => {
  it('should have correct enum values', () => {
    expect(ProgressSizeEnum.Small).toEqual('sm');
    expect(ProgressSizeEnum.Medium).toEqual('md');
    expect(ProgressSizeEnum.Large).toEqual('lg');
  });
});

describe('Progress Size Input', () => {
  it('should allow small Progress size', () => {
    const size: ProgressSize = ProgressSizeEnum.Small;
    expect(size).toEqual(ProgressSizeEnum.Small);
  });

  it('should allow medium Progress size', () => {
    const size: ProgressSize = ProgressSizeEnum.Medium;
    expect(size).toEqual(ProgressSizeEnum.Medium);
  });
  it('should allow large Progress size', () => {
    const size: ProgressSize = ProgressSizeEnum.Large;
    expect(size).toEqual(ProgressSizeEnum.Large);
  });
});

describe('Progress Ring Calculation', () => {
  let component: ProgressComponent;
  let mockCdr: any;
  beforeEach(() => {
    mockCdr = jasmine.createSpyObj('ChangeDetectorRef', ['detectChanges']);
    component = new ProgressComponent(mockCdr);
  });


  it('should calculate stroke values correctly and return true if values change', () => {
    component.percentage = 50;
    component.calculateStrokeDashArray = '';
    component.calculateStrokeDashOffset = '';
    const result = component.calculateStrokeValues();
    expect(result).toBe(true);
    expect(component.calculateStrokeDashArray).toBe(`${(100 - component.percentage) * 2 * Math.PI * 11 / 100} ${2 * Math.PI * 11}`);
    expect(component.calculateStrokeDashOffset).toBe(`-${component.percentage * 2 * Math.PI * 11 / 100}`);
  });

  it('should not update values and return false if values remain the same', () => {
    // Set initial values
    component.percentage = 50;
    component.calculateStrokeDashArray = `${(100 - component.percentage) * 2 * Math.PI * 11 / 100} ${2 * Math.PI * 11}`;
    component.calculateStrokeDashOffset = `-${component.percentage * 2 * Math.PI * 11 / 100}`;

    const result = component.calculateStrokeValues();
    expect(result).toBe(false);
    expect(component.calculateStrokeDashArray).toBe(`${(100 - component.percentage) * 2 * Math.PI * 11 / 100} ${2 * Math.PI * 11}`);
    expect(component.calculateStrokeDashOffset).toBe(`-${component.percentage * 2 * Math.PI * 11 / 100}`);
  });
});
