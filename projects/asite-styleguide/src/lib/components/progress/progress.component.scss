@import "../../../../assets/scss/styles.scss";

// Progress Size
$progress-bar-sm: 4px;
$progress-bar-md: 8px;
$progress-bar-lg: 16px;

.progress-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  font-family: $fontFamily;

  .progress-bar {
    position: relative;
    overflow: hidden;
    width: 200px;
    border-radius: 4px;
    background: var(--lib-color-secondary, #ECEBFC);

    .progress-bar-inner {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      background: var(--lib-color-tertiary, #1D1878);

      &.isError{
        background-color:var( --lib-color-red-4,#E43B2C);
      }
    }

    &.sm {
      height: $progress-bar-sm;
    }
    &.md {
      height: $progress-bar-md;
    }
    &.lg {
      height: $progress-bar-lg;
    }
  }

  .progress-ring {
    display: inline-flex;
    gap: 8px;
    align-items: center;
    border-radius: 50%;
    justify-content: flex-start;
    .ring-svg {
      transform: rotate(-90deg);
      &.sm {
        width: 24px;
        height: 24px;
      }
      &.md {
        width: 34px;
        height: 34px;
      }
      &.lg {
        width: 44px;
        height: 44px;
      }
    }
  }

  .error-label{
    color:var(--lib-color-red-2, #8E0000);
  }
}
