<div class="progress-container">
  <ng-container *ngIf="type==='bar'; else ringContent">
    <div class="progress-bar" [ngClass]="[size || '']">
      <div class="progress-bar-inner" [ngClass]="{'isError':isError}" [ngStyle]="{'width':percentage +'%'}"></div>
    </div>
    <div *ngIf="label" [ngClass]="{'error-label':isError}" class="progress-bar-label">{{label}} {{percentage}}%</div>
  </ng-container>
  <ng-template #ringContent>
    <div class="progress-ring">
      <svg class="ring-svg" xmlns="http://www.w3.org/2000/svg" [ngClass]="size" viewBox="0 0 24 24" fill="none">
        <circle cx="12" cy="12" r="11" [ngStyle]="{'stroke':isError?'#E43B2C':'#1D1878'}" stroke-width="1.9"
          fill="none" />
        <circle cx="12" cy="12" r="11" [attr.stroke-dasharray]="calculateStrokeDashArray"
          [attr.stroke-dashoffset]="calculateStrokeDashOffset" stroke="#ECEBFC" stroke-width="2" fill="none" />
      </svg>
      <div *ngIf="label" [ngClass]="{'error-label':isError}" class="progress-ring-label">{{label}} {{percentage}}%
      </div>
    </div>
  </ng-template>
</div>
