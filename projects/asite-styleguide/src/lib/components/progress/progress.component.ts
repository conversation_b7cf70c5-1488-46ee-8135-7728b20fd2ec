import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, Input, SimpleChanges } from '@angular/core';
import { ProgressSize,ProgressSizeEnum } from './progress.model';

@Component({
  standalone: true,
  imports:[CommonModule],
  selector: 'lib-progress',
  templateUrl: './progress.component.html',
  styleUrls: ['./progress.component.scss']
})
export class ProgressComponent{

  @Input() label: string = '';

  @Input() size: ProgressSize = ProgressSizeEnum.Medium;

  @Input() type: string = '';

  @Input() isError: boolean = false;

  @Input() percentage: number = 0;

  calculateStrokeDashArray: string = '';

  calculateStrokeDashOffset: string = '';

  constructor(private cdr: ChangeDetectorRef) {}

  ngDoCheck(): void {
    if (this.calculateStrokeValues()) {
      // Manually trigger change detection when values are updated
      this.cdr.detectChanges();
    }
  }
  /* Changes on Calculation of Ring */
  calculateStrokeValues():boolean {
    const newDashArray = `${(100 - this.percentage) * 2 * Math.PI * 11 / 100} ${2 * Math.PI * 11}`;
    const newDashOffset = `-${this.percentage * 2 * Math.PI * 11 / 100}`;

    if (newDashArray !== this.calculateStrokeDashArray || newDashOffset !== this.calculateStrokeDashOffset) {
      this.calculateStrokeDashArray = newDashArray;
      this.calculateStrokeDashOffset = newDashOffset;
      return true;
    }
    return false;
  }
}
