@import '../../../../assets/scss/styles.scss';

.search-panel-container{
  display: flex;
  width: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 1px;
  font-family: $fontFamily;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  position: relative;

  /* Search Bar */
  .searchBox {
    width: 100%; /* Ensure the search box expands to the available width of the parent */
    height: 2.625rem;
    display: flex;
    align-items: center;
    color: var(--lib-color-gray,#616161);
    background-color: var(--lib-color-gray-7,#FAFAFA);
    text-align: left;
    font-feature-settings: 'clig' off, 'liga' off;
    gap: 8px;
    overflow: hidden;
    padding: 9px 12px;
    border-radius: 8px;
    box-sizing: border-box;
    justify-content: space-between;
    margin: 0 auto;
    border: 1px solid transparent;
    position: relative; /* Added position to keep icon fixed */

    &:hover {
      border: 1px solid var(--lib-color-gray,#616161);
    }

    &:focus-within {
      box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
    }

    &.disabled {
      background: var(--lib-color-gray-6,#E9E9E9);
      color: var(--lib-color-gray-4,#8D8D8D);
      pointer-events: none;
    }

    .inputContainer {
      display: flex;
      width: 100%;
      position: relative;
    }

    .inputBox {
      width: 100%;
      flex-grow: 1;
      background-color: transparent;
      color: var(--lib-color-gray, #616161);
      border: none;
      padding-right: 2rem;
      box-sizing: border-box;

      &:focus {
        outline: none;
      }

      &::placeholder {
          color: var(--lib-color-gray,#616161);
      }
        &.disabled,::-webkit-input-placeholder{
          background: var(--lib-color-gray-6,#E9E9E9);
          color: var(--lib-color-gray-4,#8D8D8D);
        pointer-events: none;
      }
    }

    .clearIcon {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
    }
  }

  /* search options */
  .suggestions{
    position: absolute;
    top: 100%;
    z-index: 999;
    max-height: 100vh;
    overflow-y: auto;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
    border-radius: 0px 8px 8px 8px;
    width: 100%;
    min-width: 15rem;
    ul{
      padding: 0;
      margin: 0;
      background-color: var(--lib-color-gray-7,#FAFAFA);
      border-radius: 8px 8px 8px 8px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 1px;
      align-items: flex-start;
      box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);

      .searchOption{
        display: flex;
        width: inherit;
        padding: 16px 12px;
        box-sizing: border-box;
        align-items: center;
        gap: 10px;
        align-self: stretch;
        list-style: none;
        justify-content: space-between;
        cursor: pointer;

        .label{
          color: var(--lib-color-black,#0C1315);
          font-feature-settings: 'clig' off, 'liga' off;
          font-size: 0.875rem;
          flex: auto;
        }

        .recentOptionLabel{
          font-style: italic;
          font-weight: 300;
        }

        .iconoir-clock{
          font-size: 1.25rem;
        }

        .iconoir-xmark-circle{
          font-size: 1.375rem;
          cursor: pointer;
        }
      }

      .showAll{
        display: flex;
        padding: 12px;
        align-items: center;
        gap: 10px;
        align-self: stretch;

        .label{
          color: var(--lib-color-primary, #4940D7);
          font-feature-settings: 'clig' off, 'liga' off;
          font-size: 0.875rem;
          text-decoration-line: underline;
          cursor: pointer;
        }
      }
    }
  }
}
