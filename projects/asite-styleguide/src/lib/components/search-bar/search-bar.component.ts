import {
  Component,
  Input,
  EventEmitter,
  Output,
  OnInit,
  ElementRef,
  ViewEncapsulation,
  ViewChild,
} from '@angular/core';
import { SearchOptionList, TagComponent, TagList } from '../index';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { debounce } from 'lodash';

@Component({
  standalone: true,
  imports: [CommonModule, FormsModule,TagComponent, MatProgressSpinnerModule],
  selector: 'lib-search-bar',
  templateUrl: './search-bar.component.html',
  styleUrls: ['./search-bar.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class SearchBarComponent implements OnInit {
  @Input() placeholderText: string = '';

  @Input() clearTitle: string = 'Clear text';

  @Input() searchOptionList: SearchOptionList[] = [];

  @Input() isDisabled = false;

  /**
   * @description Fetch search results from remote
   * @type {boolean}
   * @memberof SearchBarComponent
   */
  @Input() searchOnServer: boolean = false;
  
  /**
   * @description Search callback to get data from server
   * @type {Function}
   * @memberof SearchBarComponent
   */
  @Input() fetchSearchOptions: Function;
  
  /**
   * @description True if searching on
   * @memberof SearchBarComponent
   */
  isFetchingFromServer = false;

  searchText: string = '';

  filteredSearchOptions: SearchOptionList[] = [];

  recentOptionList: SearchOptionList[] = [];

  selectedSuggestions: TagList[] = [];

  showAllOptions: boolean = false;

  isSearchFocused: boolean = false;

  isSuggestionOver: boolean = false;

  hasRecentOptions: boolean = true;

  /* Emit events on various operations */
  @Output() onClearClick = new EventEmitter<Event>();

  @Output() searchTextChangedEvent = new EventEmitter<string>();

  @Output() onBlur = new EventEmitter<string>();

  @Output() selectedSuggestionEvent = new EventEmitter<any>();

  @Output() tagRemovedEvent = new EventEmitter<Event>();

  @Output() removeTagFromBackSpaceEvent = new EventEmitter<Event>();

  @ViewChild('searchInput') searchInput: ElementRef;

  /* Bebounce method on search value changes */
  OnServerFolderSearch: any;

  /* Search functionality */
  ngOnInit(): void {
    if (!this.searchText) {
      this.clearSearchText();
    }

    if(this.searchOnServer) {
      this.OnServerFolderSearch = debounce(this.getSearchResultsFromServer, 300);
    }
  }

  /* Select and Remove Suggestion for search */
  addSelectedSuggestion(searchOpt: any): void {
    let label = searchOpt.label;
    if(this.searchOnServer) {
      this.searchText = '';
      this.isSuggestionOver = false;
      this.selectedSuggestionEvent.emit(searchOpt);
      return;
    }

    this.selectedSuggestions = [];
    if (this.searchText!=='' || label.toLowerCase().includes(this.searchText.trim().toLowerCase())) {
      let tag: TagList = {
        Id: 1,
        Label: label,
        Icon: ''
      };
      this.selectedSuggestions = [tag];
      this.searchText = '';
      this.selectedSuggestionEvent.emit(label);
    }
  }

  removeSelectedSuggestion(event: Event): void {
    event.stopPropagation();
    const targetElement = (event.target as HTMLElement);
    const isIconClick = targetElement.tagName === 'I';

    if (isIconClick) {
      this.clearSearchText();
    }
    this.tagRemovedEvent.emit(event);
  }

  removeTagFromBackSpace(event: KeyboardEvent): void {
    if (event.key === 'Backspace' && this.searchText === '') {
      this.clearSearchText();
      this.removeTagFromBackSpaceEvent.emit(event);
    }
  }

  /* Clear SearchBox */
  clearSearchText(): void {
      this.searchText = '';
      this.selectedSuggestions = [];
      this.filterSearchOptions();
      if (this.searchInput?.nativeElement) {
        this.searchInput.nativeElement.focus();
      }
        this.onClearClick.emit(event);
        this.searchTextChangedEvent.emit(this.searchText);
    }

  /* Filter options based on letter match and show recent options if searchText is empty */
  filterSearchOptions() {
    if(this.searchOnServer && this.searchText) {
      this.OnServerFolderSearch();
      return;
    }

    this.filteredSearchOptions = [];
    if (this.searchText.trim() === '') {
      this.filteredSearchOptions = [];
      this.hasRecentOptions = true;
    } else {
      this.searchOptionList.forEach((option) =>{
        if (option.label.toString().trim().toLowerCase().includes(this.searchText.toLowerCase())) {
          this.filteredSearchOptions.push(option);
        }
      });
      this.hasRecentOptions = false;
    }
    this.searchTextChangedEvent.emit(this.searchText);
  }

  /* Get search results from server */
  getSearchResultsFromServer() {
    this.isFetchingFromServer = true;
    this.fetchSearchOptions(this.searchText)?.subscribe(
      (filteredSearchOpt) => {
        this.isFetchingFromServer = false;
        this.filteredSearchOptions = filteredSearchOpt;
        this.hasRecentOptions = !filteredSearchOpt.length;
      },
      () => {
        this.isFetchingFromServer = false;
      }
    );
  }

  /* Remove Recent Option */
  removeRecentOption(index: number): void {
    this.recentOptionList.splice(index, 1);
  }

  /**
   * @description emits search bar value on blur
   */
  emitBlur(){
    this.isSearchFocused = false;
    this.onBlur.emit(this.searchText);
  }
}
