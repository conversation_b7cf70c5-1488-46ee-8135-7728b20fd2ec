<div class="search-panel-container">
  <div class="searchBox" [class.disabled]="isDisabled">
    <i class="iconoir-search"></i>
    <lib-tag *ngIf="selectedSuggestions" [size]="'md'" [tagList]="selectedSuggestions" [isClosable]="true"
      [type]="'primary'" (click)="removeSelectedSuggestion($event)"></lib-tag>
    <div class="inputContainer">
      <input #searchInput id="searchInput" type="text" class="inputBox" [(ngModel)]="searchText" autocomplete="off"
        [placeholder]="placeholderText" (focus)="isSearchFocused=true;filterSearchOptions()" (keyup)="this.filterSearchOptions();
    this.searchTextChangedEvent.emit(this.searchText);" (keydown)="removeTagFromBackSpace($event)"
        [class.focused]="isSearchFocused" (blur)="emitBlur()" [class.disabled]="isDisabled"
        aria-label="Search" title="Search" />
      <i *ngIf="(selectedSuggestions.length || searchText) && !isFetchingFromServer" class="iconoir-xmark-circle clearIcon"
        (click)="clearSearchText(searchInput);" [title]="clearTitle"></i>
      <mat-spinner *ngIf="isFetchingFromServer" class="mat-spinner-loader-color" diameter="15"></mat-spinner>
    </div>
  </div>
  <ng-container *ngIf="searchText.length > 0 || (filteredSearchOptions && isSearchFocused) || isSuggestionOver">
    <div class="suggestions" (mouseover)="isSuggestionOver = true" (mouseleave)="isSuggestionOver = false">
      <ul>
        <ng-container *ngFor="let searchOption of filteredSearchOptions; let i=index">
          <li *ngIf="i<5 || showAllOptions" class="searchOption" (click)="addSelectedSuggestion(searchOption)">
            <i class="iconoir-clock" *ngIf="hasRecentOptions"></i>
            <span [ngClass]="hasRecentOptions?'label recentOptionLabel':'label'">{{ searchOption.label }}</span>
            <i class="iconoir-xmark-circle" *ngIf="hasRecentOptions" (click)="removeRecentOption(i)"></i>
          </li>
        </ng-container>
        <li class="showAll" *ngIf="searchText.length > 0 && filteredSearchOptions.length > 5 && !showAllOptions"
          (click)="showAllOptions=true">
          <span class="label">
            Show all matches...
          </span>
        </li>
      </ul>
    </div>
  </ng-container>
</div>
