import { SearchOptionList} from './search-bar.model';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SearchBarComponent } from './search-bar.component';
import { FormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { TagList } from '../tag/tag.model';
import { EventEmitter } from '@angular/core';
import { of, throwError } from 'rxjs';
import * as lodash from 'lodash';

describe('SearchBarComponent', () => {
  let component: SearchBarComponent;
  let fixture: ComponentFixture<SearchBarComponent>;

  beforeEach(async () => {

    fixture = TestBed.createComponent(SearchBarComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

describe('Search Options DataType Enum', () => {
  it("should have correct enum values", () => {
    const searchOption: SearchOptionList = {
      id: 1,
      label: 'Test Label'
    };

    expect(searchOption).toBeTruthy();
    expect(searchOption.id).toBe(1);
    expect(searchOption.label).toBe('Test Label');
  })
});

describe('Recent Options DataType Enum', () => {
  it("should have correct enum values", () => {
    const recentOption: SearchOptionList = {
      id: 1,
      label: 'Test Label',
    };

    expect(recentOption).toBeTruthy();
    expect(recentOption.id).toBe(1);
    expect(recentOption.label).toBe('Test Label');
  })
});

describe('Add Selected Suggestion to SearchBar', () => {
  let component: SearchBarComponent;
  beforeEach(() => {
    TestBed.configureTestingModule({
    });
    const fixture = TestBed.createComponent(SearchBarComponent);
    component = fixture.componentInstance;
  });

  it('should add selected suggestion when searchText is empty', () => {
    const searchOpt = { id: 1, label: 'TestOption' };
    component.addSelectedSuggestion(searchOpt);
    let tags: TagList[] = [{
      Id: 1,
      Label: searchOpt.label,
      Icon: ''
    }];
    expect(component.selectedSuggestions).toEqual(tags);
    expect(component.searchText).toBe('');
  });

  it('should add selected suggestion when searchText matches part of the option', () => {
    component.searchText = 'Test';
    component.searchOnServer = false;
    const opt = { id: 2, label: 'TestOption'};
    let tags: TagList[] = [{
      Id: 1,
      Label: opt.label,
      Icon: ''
    }];
    component.addSelectedSuggestion(opt);
    expect(component.selectedSuggestions).toEqual(tags);
    expect(component.searchText).toBe('');
  });

  it('should emit selectedSuggestions when addSelectedSuggestion is called', () => {
    const opt = { id: 2, label: 'exampleOption'};
    const label = opt.label;
    spyOn(component.selectedSuggestionEvent, 'emit');
    component.addSelectedSuggestion(opt);
    expect(component.selectedSuggestionEvent.emit).toHaveBeenCalledWith(label);
  });

});

describe('Remove Selected Suggestion to SearchBar', () => {
  let component: SearchBarComponent;
  let fixture: ComponentFixture<SearchBarComponent>;
  const mockEvent = {
    stopPropagation: jasmine.createSpy(),
    target: {
      tagName: 'I',
    }
  }
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports:[FormsModule],
    });
    fixture = TestBed.createComponent(SearchBarComponent);
    component = fixture.componentInstance;
  });

  it('should stop propagation when lib-button is clicked', () =>{
    mockEvent.stopPropagation();
    expect(mockEvent.stopPropagation).toHaveBeenCalled();
  });

  it('should target <I> tag and it onClick event call clearText function', () =>{
    spyOn(component, 'clearSearchText');
    (component as any).removeSelectedSuggestion(mockEvent);
    const isIconClick = mockEvent.target.tagName;
    expect(isIconClick).toEqual('I');
    expect(component.clearSearchText).toHaveBeenCalled();
  });

  it('should emit tagRemovedEvent when remove icon is clicked', () => {
    spyOn(component, 'clearSearchText');
    const mockClickEvent = new Event('click');
    const mockElement = document.createElement('i');
    spyOnProperty(mockClickEvent, 'target').and.returnValue(mockElement);
    spyOn(component.tagRemovedEvent, 'emit');
    component.removeSelectedSuggestion(mockClickEvent);
    expect((mockClickEvent.target as HTMLElement).tagName).toEqual('I');
    expect(component.tagRemovedEvent.emit).toHaveBeenCalledWith(mockClickEvent);
    expect(component.clearSearchText).toHaveBeenCalled();
  });
});

describe('Remove Search Tag from Backspace', () => {
  let component: SearchBarComponent;
  beforeEach(() => {
    component = new SearchBarComponent();
  });

  it('should clear search text when backspace is pressed and search text is empty', function() {
    const component = new SearchBarComponent();
    component.searchText = '';
    component.removeTagFromBackSpace({ key: 'Backspace' } as KeyboardEvent);
    expect(component.searchText).toBe('');
  });

  it('should not clear search text when backspace is pressed and search text is not empty', function() {
    const component = new SearchBarComponent();
    component.searchText = 'test';
    component.removeTagFromBackSpace({ key: 'Backspace' } as KeyboardEvent);
    expect(component.searchText).toBe('test');
  });

  it('should emit removeTagFromBackSpaceEvent when Backspace key is pressed and searchText is empty', () => {
    const mockEvent = { key: 'Backspace' } as KeyboardEvent;
    spyOn(component, 'clearSearchText');
    spyOn(component.removeTagFromBackSpaceEvent, 'emit');
    component.searchText = '';
    component.removeTagFromBackSpace(mockEvent);
    expect(component.removeTagFromBackSpaceEvent.emit).toHaveBeenCalledWith(mockEvent);
    expect(component.clearSearchText).toHaveBeenCalled();
  });
  it('should not emit removeTagFromBackSpaceEvent when Backspace key is pressed and searchText is not empty', () => {
    const mockEvent = { key: 'Backspace' } as KeyboardEvent;
    spyOn(component, 'clearSearchText');
    spyOn(component.removeTagFromBackSpaceEvent, 'emit');
    component.searchText = 'non-empty';
    component.removeTagFromBackSpace(mockEvent);
    expect(component.clearSearchText).not.toHaveBeenCalled();
    expect(component.removeTagFromBackSpaceEvent.emit).not.toHaveBeenCalled();
  });
});

describe('Clear Search Text Function', () => {
  let component: SearchBarComponent;
  let fixture: ComponentFixture<SearchBarComponent>;
  beforeEach(() => {
    fixture = TestBed.createComponent(SearchBarComponent);
    component = fixture.componentInstance;
  });

  it('should set searchText and selectedSuggestions to an empty string', function() {
    const searchBarComponent = new SearchBarComponent();
    searchBarComponent.selectedSuggestions = [];
    searchBarComponent.clearSearchText();
    expect(searchBarComponent.searchText).toEqual('');
    expect(searchBarComponent.selectedSuggestions).toEqual([]);
  });
  it('should call filterSearchOptions method', function() {
    const searchBarComponent = new SearchBarComponent();
    spyOn(searchBarComponent, 'filterSearchOptions');
    searchBarComponent.clearSearchText();
    expect(searchBarComponent.filterSearchOptions).toHaveBeenCalled();
  });

  it('should focus on search input if nativeElement and focus method are available', () => {
    component.searchInput = {
      nativeElement: {
        focus: jasmine.createSpy("focus"),
      }
    }
    const mockInputElement = fixture.debugElement.query(By.css('input')).nativeElement;
    spyOn(component.searchInput, 'nativeElement').and.returnValue(mockInputElement);
    spyOn(mockInputElement, 'focus');
    component.clearSearchText();
    expect(component.searchInput.nativeElement.focus).toHaveBeenCalled();
  });
  it("should emit the events when clearSearchText function is called", () =>
  {
    component.searchText = '';
    const option = 'exampleOption';
    spyOn(component.onClearClick, 'emit');
    spyOn(component.selectedSuggestionEvent, 'emit');
    component.clearSearchText();
  });
});

describe('Filter Search Option Function', () => {
  let component: SearchBarComponent;
  beforeEach(() => {
    component = new SearchBarComponent();
  });

  it('filteredSearchOptions should be empty ', () =>
  {
      expect(component.filteredSearchOptions).toEqual([]);
  });

  it('should filter search options based on letter match', function() {
    const component = new SearchBarComponent();
    component.searchText = 'a';
    component.searchOptionList = [
      { id: 1, label: 'apple' },
      { id: 2, label: 'banana' },
      { id: 3, label: 'orange' }
    ];
    component.recentOptionList = [
      { id: 1, label: 'Recent Search Result'},
      { id: 2, label: 'Recent Suggestion'},
      { id: 3, label: 'Recent Project'}
    ];
    component.filterSearchOptions();
    expect(component.filteredSearchOptions).toEqual([
      { id: 1, label: 'apple' },
      { id: 2, label: 'banana' },
      { id: 3, label: 'orange' }]);
  });

  it('should handle empty searchOptionList', function() {
    const component = new SearchBarComponent();
    component.searchText = 'a';
    component.searchOptionList = [];
    component.filterSearchOptions();
    expect(component.filteredSearchOptions).toEqual([]);
  });

  it('should handle empty recentOptionList', function() {
    const component = new SearchBarComponent();
    component.searchText = '';
    component.recentOptionList = [];
    component.filterSearchOptions();
    expect(component.filteredSearchOptions).toEqual([]);
  });

  it('should filter search options based on letter match when searchText is Not empty', function() {
    const component = new SearchBarComponent();
    component.searchText = 'o';
    component.searchOptionList = [
      { id: 1, label: 'oo' },
      { id: 2, label: 'banana' },
      { id: 3, label: 'orange' }
    ];
    component.filterSearchOptions();
    expect(component.filteredSearchOptions).toEqual([
    { id: 1, label: 'oo' },
    { id: 3, label: 'orange' }]);
  });

  it("should emit the events when filteredSearchOptions function is called", () =>
  {
    component.searchText = 'd';
    const option = 'exampleOption';
    spyOn(component.searchTextChangedEvent, 'emit');
    component.filterSearchOptions();
    expect(component.searchTextChangedEvent.emit).toHaveBeenCalledWith(component.searchText);
  });
});

describe('Remove Recent option Function', () => {
  let component: SearchBarComponent;
  beforeEach(() => {
    component = new SearchBarComponent();
  });

  it('should remove the recent option at the specified index from the recentOptionList', function() {
    const component = new SearchBarComponent();
    component.recentOptionList = [
      { id: 1, label: 'Recent Search Result'},
      { id: 2, label: 'Recent Suggestion'}
    ];
    component.removeRecentOption(1);
    expect(component.recentOptionList).toEqual([{ id: 1, label: 'Recent Search Result'}]);
  });

  it('should not modify the recentOptionList when it is empty', function() {
    const component = new SearchBarComponent();
    component.recentOptionList = [];
    component.removeRecentOption(0);
    expect(component.recentOptionList).toEqual([]);
  });

  it('emitBlur :: should emit input value on blur', () => {
    component.searchText = 'test';
    component.emitBlur();
    expect(component.isSearchFocused).toBeFalse();
  });

  it('should emit selectedSuggestionEvent and clear searchText if searchOnServer is true', () => {
    const searchOpt: any = { label: 'test-label' };
    component.selectedSuggestionEvent = new EventEmitter<any>();
    component.searchOnServer = true;
    component.searchText = 'something';

    spyOn(component.selectedSuggestionEvent, 'emit');

    component.addSelectedSuggestion(searchOpt);

    expect(component.searchText).toBe('');
    expect(component.selectedSuggestionEvent.emit).toHaveBeenCalledWith(searchOpt);
  });

  it('should call fetchSearchOptions and update filteredSearchOptions (successful case)', () => {
    const mockResults: any = [{ label: 'Result1' }, { label: 'abc' }];
    component.searchOnServer = true;
    component.searchText = 'abc';
    component.fetchSearchOptions = jasmine.createSpy().and.returnValue(of(mockResults));
    component.OnServerFolderSearch = jasmine.createSpy();

    component.filterSearchOptions();

    expect(component.isFetchingFromServer).toBeFalse();
    expect(component.hasRecentOptions).toBeTrue();
  });

  it('should handle error in fetchSearchOptions and set isFetchingFromServer to false', () => {
    component.searchOnServer = true;
    component.searchText = 'error-case';
    component.OnServerFolderSearch = jasmine.createSpy();
    component.fetchSearchOptions = jasmine.createSpy().and.returnValue(throwError(() => new Error('API Error')));

    component.filterSearchOptions();

    expect(component.isFetchingFromServer).toBeFalse();
  });

  describe('getSearchResultsFromServer', () => {
    beforeEach(() => {
      component.searchText = 'Test';
      component.fetchSearchOptions = jasmine.createSpy();
    });

    it('should fetch and update search results on success', () => {
      const mockResult: any = [{ label: 'Option 1' }];
      (component.fetchSearchOptions as jasmine.Spy).and.returnValue(of(mockResult));

      component.getSearchResultsFromServer();

      expect(component.isFetchingFromServer).toBeFalse();
      expect(component.filteredSearchOptions).toEqual(mockResult);
      expect(component.hasRecentOptions).toBeFalse();
    });

    it('should handle empty results correctly', () => {
      const mockResult: any[] = [];
      (component.fetchSearchOptions as jasmine.Spy).and.returnValue(of(mockResult));

      component.getSearchResultsFromServer();

      expect(component.isFetchingFromServer).toBeFalse();
      expect(component.filteredSearchOptions).toEqual([]);
      expect(component.hasRecentOptions).toBeTrue();
    });

    it('should set isFetchingFromServer to false on error', () => {
      (component.fetchSearchOptions as jasmine.Spy).and.returnValue(throwError(() => new Error('Error')));

      component.getSearchResultsFromServer();

      expect(component.isFetchingFromServer).toBeFalse();
    });

    it('should do nothing if fetchSearchOptions returns undefined/null', () => {
      (component.fetchSearchOptions as jasmine.Spy).and.returnValue(undefined);

      component.getSearchResultsFromServer();

      expect(component.isFetchingFromServer).toBeTrue(); // it remains true since subscribe is never called
    });
  });

  describe('ngOnInit - debounce OnServerFolderSearch', () => {
    beforeEach(() => {
      spyOn(component, 'getSearchResultsFromServer');
      spyOn(lodash, 'debounce').and.callFake(fn => fn); // Fake debounce for test
    });

    it('should debounce getSearchResultsFromServer when searchOnServer is true', () => {
      component.searchOnServer = true;

      component.ngOnInit();

      expect(lodash.debounce).toHaveBeenCalledWith(component.getSearchResultsFromServer, 300);
      expect(component.OnServerFolderSearch).toBeDefined();
    });

    it('should not debounce when searchOnServer is false', () => {
      component.searchOnServer = false;

      component.ngOnInit();

      expect(component.OnServerFolderSearch).toBeUndefined();
    });
  });
});
