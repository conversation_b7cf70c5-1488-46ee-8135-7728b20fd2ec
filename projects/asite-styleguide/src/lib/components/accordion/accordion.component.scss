.accordion-container {
  border-radius: 10px;
  cursor: pointer;
  outline: 1px solid var(--lib-color-gray-5, #c5c5c5);
  width: 480px;

  /* Style for Accordion-item component */
  ::ng-deep lib-accordion-item:nth-child(1) {
    .accordion {
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
      .accordion-header:hover {
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
      }
    }
  }
  ::ng-deep lib-accordion-item:nth-last-child(1) {
    .accordion {
      .accordion-header{
        padding:12px 12px 12px 16px;
      }
      border-bottom-left-radius: 10px;
      border-bottom-right-radius: 10px;
      .accordion-header:hover {
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
      }
    }
    .accordion-separator-middle {
      display: none;
    }
  }
}
