import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { AccordionComponent } from '../../index';

@Component({
  selector: 'lib-accordion-item',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './accordion-item.component.html',
  styleUrl: './accordion-item.component.scss',
})
export class AccordionItemComponent {
  @Input() expanded: boolean = false;

  @Input() accordionTitle: string = '';

  @Output() accordionEvent = new EventEmitter<boolean>();

  constructor(private accordion: AccordionComponent) {}

  toggleAccordion(): void {
    this.expanded = !this.expanded;
    if (this.expanded) {
      this.accordion.selectItem(this);
    }
    this.accordionEvent.emit(this.expanded);
  }
}
