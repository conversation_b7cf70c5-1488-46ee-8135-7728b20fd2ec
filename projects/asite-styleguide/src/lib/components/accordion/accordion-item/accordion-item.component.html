<div class="accordion" [style]="expanded ? 'gap: 12px;' : 'gap: 0;'">
  <div class="accordion-header" (click)="toggleAccordion()">
    <div *ngIf="accordionTitle">{{accordionTitle}}</div>
    <ng-content *ngIf="accordionTitle==''" select="[accordionTitle]"></ng-content>
    <i class="iconoir-nav-arrow-right rotate down" [ngClass]="{'down': expanded}"></i>
  </div>
  <ng-container *ngIf="expanded">
    <div class="accordion-content">
      <ng-content></ng-content>
    </div>
  </ng-container>
</div>
<div class="accordion-separator-middle"></div>