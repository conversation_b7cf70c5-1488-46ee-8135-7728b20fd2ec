@import "../../../../../assets/scss/styles.scss";

.accordion {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  flex: 1 0 0;
  flex-shrink: 0;
  align-self: stretch;
  min-height: 47px;

  .accordion-header {
    display: flex;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    padding: 11px 12px 12px 16px;
    justify-content: space-between;
    transition: all 0.3s ease;
    cursor: pointer;
    font-family: $fontFamily;

    i {
      font-size: 1.5rem;
    }
    .rotate {
      -moz-transition: all 0.15s linear;
      -webkit-transition: all 0.15s linear;
      transition: all 0.15s linear;
    }
    .rotate.down {
      -moz-transform: rotate(90deg);
      -webkit-transform: rotate(90deg);
      transform: rotate(90deg);
    }
    &:hover {
      background: var(--lib-color-secondary, #ecebfc);
    }
  }

  .accordion-content {
    display: flex;
    padding: 0px 12px 12px 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    flex-shrink: 0;
    overflow: hidden;
    transition: max-height 0.2s ease-out;
  }
}
.accordion-separator-middle {
  width: 100%;
  border-bottom: 1px solid var(--lib-color-gray-5, #c5c5c5);
}
