import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AccordionItemComponent } from './accordion-item.component';
import { AccordionComponent } from '../accordion.component';

describe('AccordionItemComponent', () => {
  let component: AccordionItemComponent;
  let fixture: ComponentFixture<AccordionItemComponent>;
  let accordionComponent = AccordionComponent;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      providers: [
        { provide: AccordionComponent, useValue: accordionComponent },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AccordionItemComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('should toggle accordion', () => {
    const accordionComponentMock = {
      selectItem: jasmine.createSpy('selectItem'),
    };
    const accordionItem = new AccordionItemComponent(
      accordionComponentMock as any
    );
    accordionItem.expanded = false;

    accordionItem.toggleAccordion();

    expect(accordionItem.expanded).toBe(true);
    expect(accordionComponentMock.selectItem).toHaveBeenCalledWith(
      accordionItem
    );
  });
});

describe('AccordionItemComponent expanded Input', () => {
  let component: AccordionItemComponent;
  let fixture: ComponentFixture<AccordionItemComponent>;
  let accordionComponent = AccordionComponent;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      providers: [
        { provide: AccordionComponent, useValue: accordionComponent },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AccordionItemComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should have default value of false for expanded property', () => {
    expect(component.expanded).toBe(false);
  });

  it('should set expanded property to true', () => {
    component.expanded = true;
    expect(component.expanded).toBe(true);
  });

  it('should handle setting disabled property to false', () => {
    component.expanded = true;
    component.expanded = false;
    expect(component.expanded).toBe(false);
  });
});

describe('AccordionItemComponent accordionTitle Input', () => {
  let component: AccordionItemComponent;
  let fixture: ComponentFixture<AccordionItemComponent>;
  let accordionComponent = AccordionComponent;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      providers: [
        { provide: AccordionComponent, useValue: accordionComponent },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AccordionItemComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be string', () => {
    expect(typeof component.accordionTitle).toBe('string');
  });

  it('should update the text when the input changes', () => {
    const mockLabel = 'Custom Label';
    component.accordionTitle = 'Custom Label';
    fixture.detectChanges();
    expect(component.accordionTitle).toContain(mockLabel);
  });
});

describe('AccordionItemComponent accordionEvent Output', () => {
  let component: AccordionItemComponent;
  let fixture: ComponentFixture<AccordionItemComponent>;
  let accordionComponent = AccordionComponent;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      providers: [
        { provide: AccordionComponent, useValue: accordionComponent },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AccordionItemComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should emit an event when clicked', () => {
    spyOn(component.accordionEvent, 'emit');
    const event = false;
    component.accordionEvent.emit(event);
    expect(component.accordionEvent.emit).toHaveBeenCalledWith(event);
  });
});
