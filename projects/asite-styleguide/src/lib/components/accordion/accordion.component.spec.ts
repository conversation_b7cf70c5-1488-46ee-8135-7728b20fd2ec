import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AccordionComponent } from './accordion.component';
import { AccordionItemComponent } from './accordion-item/accordion-item.component';

describe('AccordionComponent', () => {
  let component: AccordionComponent;
  let accordionItemComponent: AccordionItemComponent;
  let fixture: ComponentFixture<AccordionComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        { provide: AccordionItemComponent, useValue: accordionItemComponent },
      ],
    });
    fixture = TestBed.createComponent(AccordionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize items as empty QueryList', () => {
    expect(component.items.length).toBe(0);
  });

  it('should select an item', () => {
    const accordionComponentMock = {
      selectItem: jasmine.createSpy('selectItem'),
    };

    const item1 = new AccordionItemComponent(accordionComponentMock as any);
    const item2 = new AccordionItemComponent(accordionComponentMock as any);

    component.items.reset([item1, item2]);
    component.canExpandMultiple = false;

    component.selectItem(item1);
    expect(item1.expanded).toBe(true);
    expect(item2.expanded).toBe(false);

    component.selectItem(item2);
    expect(item1.expanded).toBe(false);
    expect(item2.expanded).toBe(true);
  });
  it('should select an item', () => {
    const accordionComponentMock = {
      selectItem: jasmine.createSpy('selectItem'),
    };

    const item1 = new AccordionItemComponent(accordionComponentMock as any);
    const item2 = new AccordionItemComponent(accordionComponentMock as any);

    component.items.reset([item1, item2]);
    component.canExpandMultiple = true;

    component.selectItem(item1);
    expect(item1.expanded).toBe(true);
    expect(item2.expanded).toBe(false);

    component.selectItem(item2);
    expect(item1.expanded).toBe(true);
    expect(item2.expanded).toBe(true);
  });
});
