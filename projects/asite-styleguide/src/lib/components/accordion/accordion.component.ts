import { CommonModule } from '@angular/common';
import {
  Component,
  ContentChildren,
  Input,
  QueryList,
  SimpleChanges,
} from '@angular/core';
import { AccordionItemComponent } from './accordion-item/accordion-item.component';

@Component({
  selector: 'lib-accordion',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './accordion.component.html',
  styleUrl: './accordion.component.scss',
})

export class AccordionComponent {
  @ContentChildren(AccordionItemComponent)
  items: QueryList<AccordionItemComponent>;

  @Input() canExpandMultiple : boolean = false;
  
  selectItem(item: AccordionItemComponent) {
    if(this.canExpandMultiple) {
      item.expanded = true;
    } else {
      this.items.forEach((i) => {
        i.expanded = i === item;
      });
    }
  }
}
