import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MenuComponent } from './menu.component';
import { MenuItem, MenuType, MenuTypeEnum } from './menu.model';

describe('MenuComponent', () => {
  let component: MenuComponent;
  let fixture: ComponentFixture<MenuComponent>;

  beforeEach(async () => {
    fixture = TestBed.createComponent(MenuComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have Primary type as MenuTypeEnum.Primary', () => {
    expect(component.type).toEqual(MenuTypeEnum.Primary);
  });

  it('should accept a different type through input', () => {
    const expectedType: MenuType = MenuTypeEnum.Secondary;
    component.type = expectedType;
    fixture.detectChanges();

    expect(component.type).toEqual(expectedType);
  });

  it('should have default value of an empty string for icon property', () => {
    expect(component.icon).toEqual('');
  });

  it('should set a valid icon', () => {
    const validIcon = 'iconoir-lens';
    component.icon = validIcon;
    expect(component.icon).toEqual(validIcon);
  });

  it('should have default value of an empty string for subMenuIcon property', () => {
    expect(component.subMenuIcon).toEqual('');
  });

  it('should accept different menu items through input', () => {
    const expectedMenuItems: MenuItem[] = [
      { id: "1", title: 'Tab 1', icon: '', disable: false, showSubmenu: false, data: [], callback:()=>{}, hidden: false },
      { id: "2", title: 'Tab 2', icon: '', disable: false, showSubmenu: false , data: [], callback:()=>{}, hidden: false},
      { id: "3", title: 'Tab 3', icon: '', disable: false, showSubmenu: false, data: [], callback:()=>{}, hidden: false },
      { id: "4", title: 'Tab 4', icon: '', disable: false, showSubmenu: false, data: [], callback:()=>{}, hidden: false },
      { id: "5", title: 'Tab 5', icon: '', disable: false, showSubmenu: false, data: [], callback:()=>{},  hidden: false },
    ];
    component.menuItems = expectedMenuItems;
    fixture.detectChanges();
    expect(component.menuItems).toEqual(expectedMenuItems);
  });

  it('should set a valid subMenuIcon', () => {
    const validIcon = 'iconoir-nav-arrow-right';
    component.subMenuIcon = validIcon;
    expect(component.subMenuIcon).toEqual(validIcon);
  });

  it('should have default menu items as an empty array', () => {
    expect(component.menuItems).toEqual([]);
  });

  it('should have default value of false for separator', () => {
    expect(component.separator).toBe(false);
  });

  it('should set separator to true', () => {
    component.separator = true;
    expect(component.separator).toBe(true);
  });

  it('should handle setting separator to false', () => {
    component.separator = true;
    component.separator = false;
    expect(component.separator).toBe(false);
  });

  it('should have a default alignment of "left"', () => {
    expect(component.alignment).toEqual('left');
  });

  it('should accept a different alignment of "right" through input', () => {
    const expectedAlignment = 'right';
    component.alignment = expectedAlignment;
    fixture.detectChanges();
    expect(component.alignment).toEqual(expectedAlignment);
  });

  it('should accept a different alignment of "center" through input', () => {
    const expectedAlignment = 'center';
    component.alignment = expectedAlignment;
    fixture.detectChanges();
    expect(component.alignment).toEqual(expectedAlignment);
  });

  it('should set showSubmenu to true and invoke hover callback when item is not disable', () => {
    const mockItem: MenuItem = {
      id: "1",
      title: 'Menu 1',
      icon: 'iconoir-lens',
      disable: false,
      callback: () => {},
      hover: jasmine.createSpy('hoverCallback'),
      hidden: false,
      data: [],
      showSubmenu: false,
      subMenuItems: [],
    };
    component.hover(mockItem);

    expect(mockItem.showSubmenu).toBeTruthy();
    expect(mockItem.hover).toHaveBeenCalled();
  });

  it('should not set showSubmenu to true or invoke hover callback when item is disable', () => {
    const mockMenuItems: MenuItem = {
      id: "1",
      title: 'Menu 1',
      icon: 'iconoir-lens',
      disable: false,
      callback: () => {},
      hover: jasmine.createSpy('hoverCallback'),
      hidden: false,
      data: [],
      showSubmenu: false,
      subMenuItems: [],
    };
    mockMenuItems.disable = true;
    component.hover(mockMenuItems);

    expect(mockMenuItems.showSubmenu).toBeFalsy();
    expect(mockMenuItems.hover).not.toHaveBeenCalled();
  });

  it('should set styleObj with right and position:absolute when availableSpaceRight is greater than 310', () => {
    const mockMenuItems = {
      id: "1",
      title: 'Menu 1',
      icon: 'iconoir-lens',
      disable: false,
      callback: () => {},
      hover: () => {},
      hidden: false,
      data: [],
      showSubmenu: true,
      styleObj: {
        right: '',
        position: '',
      },
      subMenuItems: [
        {
          id: "2",
          title: 'SubMenu 1',
          icon: 'iconoir-lens',
          disable: false,
          callback: () => {},
          hover: () => {},
          hidden: false,
          data: [],
          showSubmenu: false,
          subMenuItems: [],
        },
      ],
    };
    const mockLi = document.createElement('li');
    spyOn(mockLi, 'getBoundingClientRect').and.returnValue({
      left: 24,
      right: 324,
      top: 20,
      height: 0,
      width: 0,
      x: 0,
      y: 0,
      bottom: 0,
      toJSON: function () {
        throw new Error('Function not implemented.');
      },
    });
    window.innerWidth = 1300;
    component.setStyleObj(mockMenuItems, mockLi);

    expect(mockMenuItems.styleObj).toEqual({
      right: '309px',
      position: 'absolute',
    });
  });

  it('should set styleObj with left, right, and position:absolute when liPosition.left is greater than 310', () => {
    const mockMenuItems = {
      id: "1",
      title: 'Menu 1',
      icon: 'iconoir-lens',
      disable: false,
      callback: () => {},
      hover: () => {},
      hidden: false,
      data: [],
      showSubmenu: true,
      styleObj: {
        left: '',
        right: '',
        position: '',
      },
      subMenuItems: [
        {
          id: "2",
          title: 'SubMenu 1',
          icon: 'iconoir-lens',
          disable: false,
          callback: () => {},
          hover: () => {},
          hidden: false,
          data: [],
          showSubmenu: false,
          subMenuItems: [],
        },
      ],
    };
    const mockLi = document.createElement('li');
    spyOn(mockLi, 'getBoundingClientRect').and.returnValue({
      left: 400,
      right: 800,
      top: 20,
      height: 0,
      width: 0,
      x: 0,
      y: 0,
      bottom: 0,
      toJSON: function () {
        throw new Error('Function not implemented.');
      },
    });
    window.innerWidth = 800;
    component.setStyleObj(mockMenuItems, mockLi);

    expect(mockMenuItems.styleObj).toEqual({
      left: 'auto',
      right: '309px',
      position: 'absolute',
    });
  });

  it('should set styleObj with background, left, top, and z-index when left space is less than 310 and availableSpaceRight is less than 310', () => {
    const mockMenuItems1 = {
      id: "1",
      title: 'Menu 1',
      icon: 'iconoir-lens',
      disable: false,
      callback: () => {},
      hover: () => {},
      hidden: false,
      data: [],
      showSubmenu: true,
      styleObj: {
        background: '',
        left: '',
        top: '',
        'z-index': '',
      },
      subMenuItems: [
        {
          id: "2",
          title: 'SubMenu 1',
          icon: 'iconoir-lens',
          disable: false,
          callback: () => {},
          hover: () => {},
          hidden: false,
          data: [],
          showSubmenu: false,
          subMenuItems: [],
        },
      ],
    };
    const mockLi = document.createElement('li');
    spyOn(mockLi, 'getBoundingClientRect').and.returnValue({
      left: 24,
      right: 324,
      top: 20,
      height: 0,
      width: 0,
      x: 0,
      y: 0,
      bottom: 0,
      toJSON: function () {
        throw new Error('Function not implemented.');
      },
    });

    window.innerWidth = 500;
    component.setStyleObj(mockMenuItems1, mockLi);

    expect(mockMenuItems1.styleObj).toEqual({
      background: 'white',
      top: '40px',
      left: '-8px',
      'z-index': '1',
    });
  });

  it('should toggle item.checked and emit when multi-select is true and item is not disabled', () => {
    const mockMenuItems = {
      id: "1",
      title: 'Menu 1',
      icon: 'iconoir-lens',
      disable: false,
      checked: false,
      callback: () => {},
      hover: () => {},
      hidden: false,
      data: [],
      showSubmenu: true,
      styleObj: {
        right: '',
        position: '',
      }
    };

    component.isMultiSelect = true;
    component.toggleSelection(mockMenuItems);
    expect(mockMenuItems.checked).toBeTrue();
  });

  it('should emit item directly when isMultiSelect is false', () => {
    const mockMenuItems = {
      id: "1",
      title: 'Menu 1',
      icon: 'iconoir-lens',
      disable: false,
      checked: false,
      callback: () => {},
      hover: () => {},
      hidden: false,
      data: [],
      showSubmenu: true,
      styleObj: {
        right: '',
        position: '',
      }
    };
    component.isMultiSelect = false;
    component.toggleSelection(mockMenuItems);
    expect(mockMenuItems.checked).toBeFalse();
  });

  it('should not toggle or emit when item is disabled and isMultiSelect is true', () => {
    const mockMenuItems = {
      id: "1",
      title: 'Menu 1',
      icon: 'iconoir-lens',
      disable: true,
      checked: false,
      callback: () => {},
      hover: () => {},
      hidden: false,
      data: [],
      showSubmenu: true,
      styleObj: {
        right: '',
        position: '',
      }
    };

    component.isMultiSelect = true;
    component.toggleSelection(mockMenuItems);
    expect(mockMenuItems.checked).toBeFalse();
  });

  // Cleanup
  afterEach(() => {
    fixture.destroy();
  });
});

describe('Menu Types Enums', () => {
  it('should have correct enum values', () => {
    expect(MenuTypeEnum.Primary).toEqual('primary');
    expect(MenuTypeEnum.Secondary).toEqual('secondary');
  });
});
