import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { MenuItem, MenuType, MenuTypeEnum } from './menu.model';
import { CommonModule } from '@angular/common';
import { ScrollingModule } from '@angular/cdk/scrolling';

@Component({
  standalone: true,
  imports: [
    CommonModule,
    ScrollingModule
  ],
  selector: 'lib-menu',
  templateUrl: './menu.component.html',
  styleUrls: ['./menu.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MenuComponent {
  @Input() type: MenuType = MenuTypeEnum.Primary;

  @Input() icon: string = '';

  /**
   * @description Array containing menu items.
   * @memberof MenuComponent
   */
  @Input() menuItems: MenuItem[] = [];

  @Input() subMenuIcon: string = '';

  @Input() separator: boolean = false;

  @Input() alignment: string = 'left';

  /**
   * @description Default selected tab
   * @memberof MenuComponent
   */
  @Input() selectedIndex: string = '-1';

  /**
   * @description tick on active menu
   * @memberof MenuComponent
   */
  @Input() tick: boolean = false;
  
  /**
   * @description to support multi selection
   * @type {boolean}
   * @memberof MenuComponent
   */
  @Input() isMultiSelect = false;

  /**
   * @description Show selected menu item
   * @memberof MenuComponent
   */
  @Input() showSelectedMenuItem: boolean = false;

  /**
   * @description to support virtual scroll for optimized rendering
   * @type {boolean}
   * @memberof MenuComponent
   */
  @Input() isVirtualScrollSupported: boolean = false;

  /**
   * @description Defines the height of each row in the virtual scroll list
   * @type {number}
   * @memberof MenuComponent
   */
  @Input() rowHeightForVirtualScroll: number = 48;

  /**
   * @description Specifies the height of the virtual scroll container
   * @type {number}
   * @memberof MenuComponent
   */
  @Input() virtualScrollContainerHeight: string = '551px';

  /**
   * @description
   * The custom height of the menu virtual scroll container. Passed from the parent component as a string
   * @type {string}
   * @memberof MenuComponent
   */
  @Input() customHeight: string = '';

  /**
   * @description Top, Left position object of root menu
   * @memberof MenuComponent
   */
  styleObj = {
    top: '0px',
    left: '0px',
  };

  /**
   * @description on menu items hover callback
   * @param items
   * @returns
   * @memberof MenuComponent
   */
  hover(item: MenuItem) {
    if (item.disable) {
      return;
    }
    item.showSubmenu = true;
    if (item.hover) {
      item.hover(item);
    }
  }

  /**
   * @description Submenu positioning
   * @param {*} menuItems
   * @param {*} li
   * @returns
   * @memberof MenuComponent
   */
  setStyleObj(menuItems, li) {
    const liPosition = li.getBoundingClientRect();
    const availableSpaceRight = window.innerWidth - liPosition.right;

    if (menuItems.subMenuItems && menuItems.subMenuItems.length) {
      // Set the position based on available space
      if (liPosition.left < 310 && availableSpaceRight < 310) {
        menuItems.styleObj = {
          background: 'white',
          left: '-8px',
          top: '40px',
          'z-index': '1',
        };
      } else if (availableSpaceRight > 310) {
        menuItems.styleObj = {
          right: '309px',
          position: 'absolute',
        };
      } else if (liPosition.left > 310) {
        menuItems.styleObj = {
          left: 'auto',
          right: '309px',
          position: 'absolute',
        };
      }
    }
  }

  @Output() onClick = new EventEmitter<Event>();

  toggleSelection(item: any) {
    if(!this.isMultiSelect) {
      this.onClick.emit(item);
      return;
    }

    if (item.disable) {
      return;
    }

    item.checked = !item.checked;
    this.onClick.emit(item);
  }
}
