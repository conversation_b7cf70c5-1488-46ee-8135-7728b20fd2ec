export enum MenuTypeEnum {
  Primary = 'primary',
  Secondary = 'secondary',
}

export type MenuType = MenuTypeEnum.Primary | MenuTypeEnum.Secondary;

/**
 * @description Menu Item
 * @export
 * @interface MenuItem
 */
export interface MenuItem {
  id: string;
  isEmpty?: boolean;
  title: string;
  icon: string,
  disable: boolean;
  checked?: boolean;
  callback?: Function,
  hover?: Function;
  hidden: boolean,
  data: [],
  showSubmenu: boolean;
  subMenuItems?: Array<MenuItem>;
}

export interface ContextMenu {
  onInit: Function
}