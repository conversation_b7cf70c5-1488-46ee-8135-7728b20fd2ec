<ng-container >
  <div [ngClass]="[alignment]" class="menu">
    <ng-container *ngTemplateOutlet="Menu; context: {menuItems: menuItems, styleObj: styleObj}"></ng-container>
  </div>
</ng-container>


<ng-template #Menu let-menuItems="menuItems" let-styleObj="styleObj" let-parentLi="parentLi">
  <ul [ngStyle]="styleObj" [style.height]="customHeight" [ngClass]="{'subMenu': parentLi}">
    <ng-container *ngIf="!isVirtualScrollSupported">
      <ng-container *ngFor="let item of menuItems">
        <ng-container *ngTemplateOutlet="liTemplate; context: {item: item, styleObj: styleObj}"></ng-container>
      </ng-container>
    </ng-container>
    <ng-container *ngIf="isVirtualScrollSupported">
      <cdk-virtual-scroll-viewport class="cdk-virtual-scroll-container" [style.height]="virtualScrollContainerHeight" [itemSize]="rowHeightForVirtualScroll">
        <ng-container *cdkVirtualFor="let item of menuItems">
          <ng-container *ngTemplateOutlet="liTemplate; context: {item: item, styleObj: styleObj}"></ng-container>
        </ng-container>
      </cdk-virtual-scroll-viewport>
    </ng-container>
  </ul>
</ng-template>


<ng-template #liTemplate let-item="item" let-styleObj="styleObj">
  <li *ngIf="!item.hidden"
    (click)="toggleSelection(item); selectedIndex = item.id"
    [ngClass]="{'disabled':item.disable, 'greyOut':item.isEmpty}"
    [class.active]="showSelectedMenuItem && (isMultiSelect ? item.checked : selectedIndex === item.id)"
    (mouseenter)="hover(item); setStyleObj(item, li)"
    (mouseleave)="item.showSubmenu = false" #li>
    <div class="menu-items">
      <i class="iconoir-check tick-icon" *ngIf="tick" [class.active]="isMultiSelect ? item.checked : selectedIndex === item.id"></i>
      <i class="menu-icon" [class]="item.icon" *ngIf="item.icon"></i>
      <span class="label">{{ item.title }}</span>
    </div>
    <i class="menu-icon" [class]="subMenuIcon" *ngIf="type == 'secondary' && item.subMenuItems && item.subMenuItems?.length"></i>
    <ng-container *ngIf="item.subMenuItems && item.subMenuItems.length && item.showSubmenu">
      <ng-container
        *ngTemplateOutlet="Menu; context: {menuItems: item.subMenuItems, styleObj : item.styleObj, parentLi: li }">
      </ng-container>
    </ng-container>
  </li>
  <div class="menu-separator-middle" *ngIf="separator"></div>
</ng-template>