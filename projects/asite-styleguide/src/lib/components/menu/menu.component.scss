@import '../../../../assets/scss/styles.scss';

.menu {

    ul {

        /* Standard Drop Shadow */
        box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.25);
        width: 300px;
        padding: 8px 8px;
        margin: 0px;
        border-radius: 0px 0px 12px 12px; // menu-separator-bottom
        li {
            font-family: $fontFamily;
            font-size: 0.875rem;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            display: flex;
            padding: 14px 12px;
            align-items: center;
            flex: 1 0 0;
            position: relative;
            justify-content: space-between;
            cursor: pointer;

            .menu-items {
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .tick-icon {
              font-size: 1.25rem;
              visibility: hidden;

              &.active {
                visibility: visible;
              }
            }

            .menu-icon {
              font-size: 1.25rem;
            }

            &:hover {
                background: var(--lib-color-purple-6, #CFCDF4);
                border-radius: 10px;
            }

            &.active {
                background: var(--lib-color-secondary, #ecebfc);
                border-radius: 10px;
            }

            &.disabled {
                color: var(--lib-color-gray-3, #575757);
                cursor: not-allowed;
            }

            &.greyOut {
                color: var(--lib-color-gray-4, #8D8D8D);
            }
            ul{
                color: #000000;
            }
        }

        .menu-separator-middle:nth-last-child(1) {
          display: none;
        }

        ul {
            position: absolute;
            left: 309px;
            top: 0;
        }

        .cdk-virtual-scroll-container{
            overflow-y: auto;
        }
    }

    &.right {
        float: right;
    }

    &.center {
        display: flex;
        justify-content: center;
    }

    .menu-separator-middle {
        margin: 6px 12px;
        height: 1px;
        background: var(--lib-color-gray-6, #E9E9E9);
    }
}
