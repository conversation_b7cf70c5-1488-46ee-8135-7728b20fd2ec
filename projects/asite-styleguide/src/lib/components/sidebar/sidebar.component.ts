import { Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { menuItems } from './sidebar.model';
import { CommonModule } from '@angular/common';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@Component({
  selector: 'lib-sidebar',
  standalone: true,
  imports: [CommonModule, MatProgressSpinnerModule],
  templateUrl: './sidebar.component.html',
  styleUrl: './sidebar.component.scss'
})
export class SidebarComponent implements OnInit, OnDestroy {

  /**
   * @description Custom width of the sidebar
   * @type {string}
  */
  @Input() width: string = '';

  /**
   * @description Custom gap for sidebar
   * @type {string}
  */
  @Input() gap: string = '';

  /**
   * @description Title to display at the top of the sidebar
   * @type {string}
  */
  @Input() title: string = '';

  /**
   * @description Array of sidebar menu items
   * @type {Array}
  */
  @Input() menuItems: menuItems[] = [];

  /**
   * @description Function to fetch child items asynchronously
   * @type {Function}
  */
  @Input() fetchItems: Function;

  /**
   * @description set the panel class for overiding the styling;
   * @type {string}
   */
  @Input('panelClass') panelClass: string = '';

  /**
   * @description when flag true set lazyload support.
   * @type {boolean}
   */
  @Input('isLazyLoadSupport') isLazyLoadSupport: boolean = false;

  /**
   * @description Flag indicating whether the content is loading.
   * @type {boolean}
   */
  loading = false;

  /**
   * @description Flag indicating whether there is a next page available to load.
   * @type {boolean}
   */
  hasNextPage = false;

  /**
   * @description IntersectionObserver instance used to trigger lazy loading when the element is in view.
   * @type {IntersectionObserver}
   */
  observer!: IntersectionObserver;

  /**
   * @description Reference to the DOM element used for lazy loading trigger.
   * @type {ElementRef<HTMLElement>}
   * @example
   * This element is observed to load content lazily when it comes into view.
   */
  @ViewChild('lazyLoadLoader', { static: false }) lazyLoadLoader!: ElementRef<HTMLElement>;

  /**
   * @description notify parent components when an item is selected
   * @type {Output}
  */
  @Output() onItemSelect = new EventEmitter<menuItems>();

  /**
   * @description track of the currently selected item ID
   * @type {string}
  */
  selectedItemId: string = '';

  ngOnInit(): void {
    this.selectedItemId = this.menuItems.length ? this.menuItems[0].id : '';
  }

  ngOnDestroy(): void {
    //Disconnects the observer to prevent memory leaks or unnecessary operations.
    this.observer?.disconnect();
  }

  /**
   * @description Handles the expansion or collapse of a menu item. If the item is expandable and not already fetched,
   * it will trigger a fetch operation. If already fetched, it will just expand or collapse the item.
   * @param {Event} e The click event.
   * @param {menuItems} item The menu item to expand or collapse.
   */
  expandItem(e: Event, item: menuItems): void {
    e.stopPropagation();

    // Return if the item is not expandable or is already fetching data
    if (!item.expandable || item.isFetching) {
      return;
    }

    if (item.expanded) {
      // Collapse the item if it's already expanded
      item.expanded = false;
      return;
    }

    // If the item has already been fetched, just expand it and return
    if (!item.fetchFromRemote || item.isFetched) {
      item.expanded = true;
      return;
    } else {
      // If the item is expandable but data needs to be fetched
      item.isFetching = true;
      this.fetchItems(item).subscribe(
        (newItems) => {
          // On success
          this.hasNextPage = newItems.hasNextPage;
          item.items = newItems.data;
          item.isFetching = false;
          item.isFetched = true;
          item.expanded = true;
          item.fetchFromRemote = false;

          // Disconnect observer if no next page, otherwise reinitialize it
          if(!this.hasNextPage) {
            this.observer?.disconnect();
          } else {
            this.initObserver(item);
          }
        },
        () => {
          // On error
          item.isFetching = false;
          item.expanded = true;
        }
      );
    }
  }

  /**
   * @description Handles the click event on a menu item, selects it, and emits the item.
   * @param {Event} e The click event.
   * @param {any} menuItem The clicked menu item.
   */
  onItemClick(e: Event, menuItem: any): void {
    e.stopPropagation(); // Prevent event from bubbling up
    this.selectedItemId = menuItem.id; // Set the selected item's ID
    this.onItemSelect.emit(menuItem); // Emit the selected item
  }

  /**
   * @description Returns the unique identifier of the menu item for efficient rendering.
   * @param {any} menuItem The menu item to track.
   * @returns {any} The unique ID of the menu item.
   */
  trackByFn(menuItem: any): any {
    return menuItem.id;
  }

  /**
   * @description Initializes the IntersectionObserver to detect when the element comes into view
   * and triggers the loading of more items when needed.
   * @param {any} item The item object containing the data and state of the currently loaded items.
   */
  initObserver(item) {
    
    const options = {
      root: null,
      rootMargin: '10px', // Preload earlier when the item is about to come into view
      threshold: 0.1 // Trigger observer when 10% of the element is visible
    };

    // Initialize the IntersectionObserver with the defined callback and options
    this.observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting && !this.loading) {
        // If the item is visible and not already loading, proceed to load more items
        if(!item.expanded) {
          // If the item isn't expanded, reset loading states
          this.loading = false;
          this.hasNextPage = false;
          return;
        }
        // Proceed to load more items
        this.loadMoreItems(item);
      }
    }, options);

     // If the lazy load loader is available, start observing it
    if (this.lazyLoadLoader) {
      this.observer.observe(this.lazyLoadLoader.nativeElement);
    }
  }

  /**
   * @description Loads more items for the given item by calling the fetch service.
   * @param {any} item The item to load additional data for.
   */
  loadMoreItems(item) {
    this.loading = true;

    // Call the fetchItems method to get new data for the item
    this.fetchItems(item).subscribe(
      (newItems) => {
        // On success
        this.hasNextPage = newItems.hasNextPage;
        item.items = newItems.data; // Append the new items to the current list
        item.isFetching = false;
        item.isFetched = true;
        item.expanded = true;
        item.fetchFromRemote = false;

        // If there's no next page, disconnect the observer to stop unnecessary observations
        if(!this.hasNextPage) {
          this.observer?.disconnect();
        }
        this.loading = false;
      },
      () => {
        // On error
        item.isFetching = false;
      }
    );

  }

}
