<div id="sidebar-wrapper" [ngClass]="panelClass">
  <div class="sidebar-left-panel" [style.max-width]="width" [style.margin-right]="gap">
    <div class="sidebar-title" [title]="title" *ngIf="title">{{title}}</div>
    <div id="sidebar-menu-items" [class.has-title]="title">
      <div *ngFor="let menuItem of menuItems;trackBy: trackByFn;">
        <div class="parent-tree" (click)="onItemClick($event,menuItem)">
          <div [title]="menuItem.label"  class="tree-list" [ngClass]="{ 'selected-item': (selectedItemId === menuItem.id)}">
            <div class="left-group">
              <i [ngClass]="menuItem.icon || 'iconoir-folder'"></i>
              <span>{{ menuItem.label }}</span>
            </div>
            <div class="right-icons-group">
              <mat-spinner *ngIf="menuItem.isFetching && menuItem.fetchFromRemote" class="mat-spinner-loader-color" diameter="15"></mat-spinner>
              <i *ngIf="!menuItem.isFetching && menuItem.expandable" class="toggle-item" (click)="expandItem($event,menuItem)" [ngClass]="{'iconoir-nav-arrow-right': !menuItem.expanded, 'iconoir-nav-arrow-down': menuItem.expanded}"></i>
            </div>
          </div>
          <div *ngIf="menuItem.expanded && menuItem.items" class="child-items">
            <ng-template [ngTemplateOutlet]="childTemplateRef" [ngTemplateOutletContext]="{ node: menuItem.items }"></ng-template>
          </div>
        </div>
      </div>
      <div *ngIf="isLazyLoadSupport" [class.hidden]="!loading && !hasNextPage">
        <div #lazyLoadLoader class="lazy-load-loader">
          <mat-spinner  class="mat-spinner-loader-color" diameter="15"></mat-spinner>
        </div>
      </div>
    </div>
  </div>
  <div class="sidebar-right-panel">
    <ng-content select=".sidebar-right-content"></ng-content>
  </div>
</div>

<ng-template #childTemplateRef let-item="node">
  <div *ngFor="let menuItem of item;trackBy: trackByFn;">
    <div class="parent-tree" (click)="onItemClick($event,menuItem)">
      <div [title]="menuItem.label"  class="tree-list" [ngClass]="{ 'selected-item': (selectedItemId === menuItem.id)}">
        <div class="left-group">
          <span>{{ menuItem.label }}</span>
        </div>
      </div>
    </div>
  </div>
</ng-template>
