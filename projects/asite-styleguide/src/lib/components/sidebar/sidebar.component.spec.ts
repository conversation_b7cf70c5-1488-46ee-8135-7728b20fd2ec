import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SidebarComponent } from './sidebar.component';
import { of, throwError } from 'rxjs';
import { menuItems } from './sidebar.model';

describe('SidebarComponent', () => {
  let component: SidebarComponent;
  let fixture: ComponentFixture<SidebarComponent>;
  let eventMock: Event;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [SidebarComponent]
    })
      .compileComponents();

    fixture = TestBed.createComponent(SidebarComponent);
    component = fixture.componentInstance;
    eventMock = jasmine.createSpyObj('Event', ['stopPropagation']);
    component.fetchItems = jasmine.createSpy(); // mock fetchItems for loadMoreItems()
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('expandItem()', () => {
    let eventMock: Event;
    let itemMock: any;

    beforeEach(() => {
      eventMock = jasmine.createSpyObj('Event', ['stopPropagation']);
      component.fetchItems = jasmine.createSpy();
    });

    it('should stop propagation when called', () => {
      itemMock = { expandable: true, expanded: false, fetchFromRemote: false, isFetched: false };
      component.expandItem(eventMock, itemMock);
      expect(eventMock.stopPropagation).toHaveBeenCalled();
    });

    it('should return if item is not expandable', () => {
      itemMock = { expandable: false, expanded: false, isFetching: false };
      component.expandItem(eventMock, itemMock);
      // Should do nothing else
      expect(eventMock.stopPropagation).toHaveBeenCalled();
    });

    it('should return if item is already fetching', () => {
      itemMock = { expandable: true, expanded: false, isFetching: true };
      component.expandItem(eventMock, itemMock);
      expect(eventMock.stopPropagation).toHaveBeenCalled();
    });

    it('should collapse item if already expanded', () => {
      itemMock = { expandable: true, expanded: true, isFetching: false };
      component.expandItem(eventMock, itemMock);
      expect(itemMock.expanded).toBeFalse();
    });

    it('should expand item directly if already fetched or no fetch needed', () => {
      itemMock = { expandable: true, expanded: false, isFetching: false, fetchFromRemote: false, isFetched: true };
      component.expandItem(eventMock, itemMock);
      expect(itemMock.expanded).toBeTrue();
    });

    it('should fetch and expand item successfully', () => {
      itemMock = { expandable: true, expanded: false, isFetching: false, fetchFromRemote: true, isFetched: false };
      const newItems = { data: [{ label: 'Child' }], hasNextPage: false };
      (component.fetchItems as jasmine.Spy).and.returnValue(of(newItems));
      component.observer = jasmine.createSpyObj('IntersectionObserver', ['disconnect']);

      component.expandItem(eventMock, itemMock);

      expect(component.fetchItems).toHaveBeenCalledWith(itemMock);
      expect(itemMock.items).toEqual(newItems.data);
      expect(itemMock.isFetching).toBeFalse();
      expect(itemMock.isFetched).toBeTrue();
      expect(itemMock.expanded).toBeTrue();
      expect(itemMock.fetchFromRemote).toBeFalse();
      expect(component.observer.disconnect).toHaveBeenCalled();
    });

    it('should fetch and initialize observer if hasNextPage is true', () => {
      itemMock = { expandable: true, expanded: false, isFetching: false, fetchFromRemote: true, isFetched: false };
      const newItems = { data: [{ label: 'Child' }], hasNextPage: true };
      (component.fetchItems as jasmine.Spy).and.returnValue(of(newItems));
      spyOn(component, 'initObserver');

      component.expandItem(eventMock, itemMock);

      expect(itemMock.isFetched).toBeTrue();
      expect(component.initObserver).toHaveBeenCalledWith(itemMock);
    });

    it('should handle fetch error and still expand', () => {
      itemMock = { expandable: true, expanded: false, isFetching: false, fetchFromRemote: true, isFetched: false };
      (component.fetchItems as jasmine.Spy).and.returnValue(throwError(() => new Error('fail')));

      component.expandItem(eventMock, itemMock);

      expect(itemMock.isFetching).toBeFalse();
      expect(itemMock.expanded).toBeTrue();
    });
  });

  describe('onItemClick', () => {
    it('should stop event propagation, set selectedItemId, and emit selected item', () => {
      const mockMenuItem: menuItems = {
        id: '123',
        label: 'Root Item',
        icon: 'folder',
        expanded: false,
        expandable: true,
        fetchFromRemote: true,
        isFetching: false,
        isFetched: false,
        items: [
          {
            id: '1.1',
            label: 'Child Item 1',
            icon: 'file',
            expanded: false,
            expandable: false,
            fetchFromRemote: false,
            isFetching: false,
            isFetched: true,
          }
        ],
        // optional extra properties for testing purposes
        disabled: false,
        selected: false,
        customData: 'extra'
      };

      spyOn(component.onItemSelect, 'emit');

      component.onItemClick(eventMock, mockMenuItem);

      expect(eventMock.stopPropagation).toHaveBeenCalled();
      expect(component.selectedItemId).toBe('123');
      expect(component.onItemSelect.emit).toHaveBeenCalledWith(mockMenuItem);
    });
  });

  describe('trackByFn', () => {
    it('should return menuItem.id', () => {
      const item = { id: 42 };
      expect(component.trackByFn(item)).toBe(42);
    });
  });

  describe('initObserver', () => {
    let mockObserver;

    beforeEach(() => {
      mockObserver = jasmine.createSpyObj('IntersectionObserver', ['observe', 'disconnect']);
      spyOn(window as any, 'IntersectionObserver').and.returnValue(mockObserver);
    });

    it('should initialize IntersectionObserver with correct options', () => {
      component.lazyLoadLoader = {
        nativeElement: {}
      } as any;

      component.loading = false;
      component.initObserver({ expanded: true });

      expect(window.IntersectionObserver).toHaveBeenCalled();
      expect(mockObserver.observe).toHaveBeenCalledWith(component.lazyLoadLoader.nativeElement);
    });

    it('should call loadMoreItems when item is expanded & entry is intersecting', () => {
      const item = { expanded: true };
      component.loading = false;
      spyOn(component, 'loadMoreItems');

      component.initObserver(item);

      // simulate the IntersectionObserver callback
      const observerCallback = ((window.IntersectionObserver as any) as jasmine.Spy).calls.argsFor(0)[0];
      observerCallback([{ isIntersecting: true }]);

      expect(component.loadMoreItems).toHaveBeenCalledWith(item);
    });

    it('should reset loading & hasNextPage if entry is intersecting but item not expanded', () => {
      const item = { expanded: false };
      component.loading = false;
      component.hasNextPage = true;

      component.initObserver(item);

      const observerCallback = ((window.IntersectionObserver as any) as jasmine.Spy).calls.argsFor(0)[0];
      observerCallback([{ isIntersecting: true }]);

      expect(component.loading).toBeFalse();
      expect(component.hasNextPage).toBeFalse();
    });
  });

  describe('loadMoreItems', () => {
    it('should fetch items and update item on success, then disconnect observer if no next page', () => {
      const item: any = { expanded: false };
      const response = { hasNextPage: false, data: [{ label: 'Child' }] };

      component.fetchItems = jasmine.createSpy().and.returnValue(of(response));
      component.observer = jasmine.createSpyObj('IntersectionObserver', ['disconnect']);

      component.loadMoreItems(item);

      expect(component.loading).toBeFalse();
      expect(item.items).toEqual(response.data);
      expect(item.isFetching).toBeFalse();
      expect(item.isFetched).toBeTrue();
      expect(item.expanded).toBeTrue();
      expect(item.fetchFromRemote).toBeFalse();
      expect(component.observer.disconnect).toHaveBeenCalled();
    });

    it('should keep observer connected if hasNextPage is true', () => {
      const item = { expanded: false };
      const response = { hasNextPage: true, data: [{ label: 'Child' }] };

      component.fetchItems = jasmine.createSpy().and.returnValue(of(response));
      component.observer = jasmine.createSpyObj('IntersectionObserver', ['disconnect']);

      component.loadMoreItems(item);

      expect(component.loading).toBeFalse();
      expect(component.hasNextPage).toBeTrue();
      expect(component.observer.disconnect).not.toHaveBeenCalled();
    });

    it('should handle error and reset item.isFetching', () => {
      const item: any = { expanded: false };
      component.fetchItems = jasmine.createSpy().and.returnValue(throwError(() => new Error('fail')));

      component.loadMoreItems(item);

      expect(item.isFetching).toBeFalse();
    });
  });
});
