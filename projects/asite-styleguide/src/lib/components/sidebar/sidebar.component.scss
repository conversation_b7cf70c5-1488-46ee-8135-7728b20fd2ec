@import "../../../../assets/scss/styles.scss";

// General Reset for all elements
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

// Sidebar styles
#sidebar-wrapper {
  font-family: $fontFamily;
  width: 100%;
  position: relative;
  display: flex;
  overflow: auto;
  border-radius: 12px;
  height: 100%;

  .sidebar-left-panel {
    width: 100%;
    padding: 11px 0 11px 11px;
    border-radius: 12px;
    background-color: var(--lib-color-white, #ffffff);
    border: 1px solid var(--lib-color-gray-6, #E9E9E9);
  }

  .sidebar-right-panel { 
    width: 100%;
    border-radius: 12px;
  }

  .sidebar-title {
    padding: 11px;
    font-size: 16px;
    font-weight: 600;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    margin-bottom: 16px;
  }

  // Sidebar Menu Items styles
  #sidebar-menu-items {
    overflow-y: auto;
    height: 100%;
    padding-right: 11px;

    &.has-title {
      height: calc(100% - 60px);
    }

    .parent-tree {
      font-size: 14px;
      font-weight: 400;

      .tree-list {
        height: 42px;
        display: flex;
        align-items: center;
        padding: 11px;
        margin-bottom: 16px;
        position: relative;
        cursor: pointer;

        .left-group, 
        .right-icons-group {
          display: flex;
          align-items: center;
          column-gap: 10px;
        }

        .left-group span {
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          width: 111px;
        }

        .right-icons-group {
          position: absolute;
          right: 5px;
          padding: 2px 2px 2px 0px;
        }

        i {
          font-size: 20px;
        }

        &:hover {
          border-radius: 8px;
          background: var(--lib-color-light-blue-1, #9FD9FF);
        }
      }
    }

    .child-items {
      .tree-list {
        padding-left: 42px;
        margin-bottom: 16px;
        user-select: none;

        &:hover {
          border-radius: 8px;
          background: var(--lib-color-light-blue-1, #9FD9FF);
        }
      }
    }

    .lazy-load-loader {
      display: flex;
      justify-content: center;
    }
  }
}

// Selected item style
.selected-item {
  border-radius: 8px;
  background: var(--lib-color-light-blue-2, #BCE0F8);
}

.hidden {
  visibility: hidden;
}