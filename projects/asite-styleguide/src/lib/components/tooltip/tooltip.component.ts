import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, ElementRef, Input, ViewChild} from '@angular/core';
import *  as FloatingUIDOM from '@floating-ui/dom';

@Component({
  selector: 'lib-tooltip',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './tooltip.component.html',
  styleUrl: './tooltip.component.scss'
})
export class TooltipComponent {

  /**
   * @description Text which is to be displayed in tooltip;
   * @type {string}
   * @memberof TooltipComponent
   */
  @Input({alias:'tooltipText',required: true}) tooltipText: string;

  /**
   * @description Set the width of the tooltip
   * @type {string}
   * @memberof TooltipComponent
   */
  @Input('customWidth') customWidth: string;

  /**
   * @description set to true if the tooltip is to be shown on hover;
   * @type {boolean}
   * @memberof TooltipComponent
   */
  @Input('isShowOnHover') isShowOnHover: boolean = false;

  /**
   * @description for setting the arrow position of tooltip;
   * @type {}
   * @memberof TooltipComponent
   */
  @Input('setArrowPosition') setArrowPosition = 0;

  /**
   * @description set to true if the tooltip is to be shown on click;
   * @type {boolean}
   * @memberof TooltipComponent
   */
  @Input('isShowOnClick') isShowOnClick: boolean = false;


  //To Do implement arrow placement
  @Input('arrowPlacement') arrowPlacement  = '';

  /**
   * @description set this to true if the tooltip is to be shown for some error message;
   * @type {boolean}
   * @memberof TooltipComponent
   */
  @Input('isError') isError: boolean = false;

  /**
   * @description placement of the tooltip;
   * Placement: top-start, top, top-end
             bottom-start, bottom, bottom-end,
             left-start, left, left-end,
             right-start, right, right-end
   * @type {boolean}
   * @memberof TooltipComponent
   */
  @Input('placement') placement: string = '';
  
  /**
   * @description set the panel class for overiding the styling;
   * @type {string}
   * @memberof TooltipComponent
   */
  @Input('panelClass') panelClass: string = '';

  /**
   * @description used for showing the tooltip directly;
   * @memberof TooltipComponent
   */
  @Input() set show(value:boolean){
    this.directTooltipHandler(value);
  };

  /**
   * @description contains reference of the element on which the tooltip is shown;
   * @type {ReferenceElement}
   * @memberof TooltipComponent
   */
  @Input('tooltipTriggerElm') tooltipTriggerElm: FloatingUIDOM.ReferenceElement;

  /**
   * @description contains element reference of the tooltip used for placement of tooltip;
   * @type {*}
   * @memberof TooltipComponent
   */
  @ViewChild('tooltipTextElm', { static: false }) tooltipTextElm: ElementRef;

  /**
   * @description contains element reference of the tooltip arrow used for placement of arrow;
   * @type {*}
   * @memberof TooltipComponent
   */
  @ViewChild('arrowElm', { static: false }) arrowElm: ElementRef;

  /**
   * @description
   * Returns the tooltip text with newline characters (`\n`) replaced by HTML line break tags (`<br>`).
   * This allows multi-line tooltip content to be rendered correctly using [innerHTML] in the template.
   *
   * @returns {string} - The formatted tooltip text with HTML line breaks.
  */
  get formattedTooltipText(): string {
    return this.tooltipText?.replace(/\n/g, '<br>') ?? '';
  }

  /**
   * @description set to true when tooltip is be displayed
   * @type {boolean}
   * @memberof TooltipComponent
   */
  public showTooltip:boolean = false;

  /**
   * @description flag to check if tooltip currently being shown;
   * @type {boolean}
   * @memberof TooltipComponent
   */
  private isTooltipAlreadyOpen : boolean = false;


  /**
   * @description used for checking tooltip closing ,reopning
   * @type {*}
   * @memberof TooltipComponent
   */
  private toolTipTimeOut;

  constructor(private changeDetector:ChangeDetectorRef){
    this.handleMouseEvent = this.handleMouseEvent.bind(this);
    this.toggleTooltip = this.toggleTooltip.bind(this);
  }

  ngAfterViewInit() {
    if(this.tooltipTriggerElm){
      if(this.isShowOnHover){
        this._unbindBindEvent(this.tooltipTriggerElm, 'mouseenter', this.handleMouseEvent);
        this._unbindBindEvent(this.tooltipTriggerElm, 'mouseleave', this.handleMouseEvent);
      } 
      if(this.isShowOnClick) {
        this._unbindBindEvent(this.tooltipTriggerElm, 'click', this.toggleTooltip);
      }
    }
  }

  /**
   * @description handling the mouse event of trigger element
   * @private
   * @memberof TooltipComponent
   */
  private handleMouseEvent(event: Event) {
    if (event.type === 'mouseenter') {
      this.showTooltip = true;
      this.changeDetector.detectChanges();
      clearTimeout(this.toolTipTimeOut);
      if (!this.isTooltipAlreadyOpen) {
        this.updateTooltipPosition();
      }
    } else if (event.type === 'mouseleave') {
      this.toolTipTimeOut = setTimeout(() => {
        this.showTooltip = false;
        this.isTooltipAlreadyOpen = false;
        this.changeDetector.detectChanges();
      }, 200);
    }
  }

  /**
   * @description used for binding the events
   * @private
   * @memberof TooltipComponent
   */
  private _unbindBindEvent(element, type, func, isRemoveOnlyListener?){
    element?.removeEventListener(type, func);
    isRemoveOnlyListener || element?.addEventListener(type, func);
  }

  ngOnDestroy(){
    this._unbindBindEvent(this.tooltipTriggerElm, 'mouseenter', this.handleMouseEvent, true);
    this._unbindBindEvent(this.tooltipTriggerElm, 'mouseleave', this.handleMouseEvent, true);
    this._unbindBindEvent(this.tooltipTriggerElm, 'click', this.toggleTooltip, true);
  }

  /**
   * @description used for keeping the tooltip opened in case the mouse moves over the tooltip
   * @public
   * @memberof TooltipComponent
   */
  public tooltipMouseEnter(){
    clearTimeout(this.toolTipTimeOut);
  }

  /**
   * @description used for keeping the tooltip closing the tooltip if the user leaves the tooltip and does not enter the trigger element
   * @public
   * @memberof TooltipComponent
   */
  public tooltipMouseLeave(){
    this.isTooltipAlreadyOpen = true
    this.toolTipTimeOut = setTimeout(()=> {
      this.showTooltip = false;
      this.isTooltipAlreadyOpen = false;
      this.changeDetector.detectChanges();
    }, 200);
  }

  /**
   * @description used for toggeling the tooltip
   * @private
   * @memberof TooltipComponent
   */
  private toggleTooltip(){
    this.showTooltip = !this.showTooltip;
    this.changeDetector.detectChanges();
    this.showTooltip && this.updateTooltipPosition();
  }

  /**
   * @description used for showing the tooltip directly
   * @private
   * @memberof TooltipComponent
   */
  private directTooltipHandler(isShow){
    this.showTooltip = isShow;
    this.changeDetector.detectChanges();
    this.showTooltip && this.updateTooltipPosition();
  }

  /**
   * @description handle the tooltip positioning
   * @private
   * @memberof TooltipComponent
   */
  private async updateTooltipPosition() {
    if(!this.tooltipTriggerElm){
      return false;
    }
    let tooltipTextEle = this.tooltipTextElm.nativeElement;
    let arrowElement = this.arrowElm.nativeElement;

    let obj = {
      middleware: [
        FloatingUIDOM.offset(11),
        FloatingUIDOM.arrow({ element: arrowElement }),
      ]
    };
    
    if(this.placement){
      obj['placement'] = this.placement;
    } else {
      obj.middleware.push(...[
        FloatingUIDOM.shift(),
        FloatingUIDOM.autoPlacement()
      ]);
    }

    const { x, y, middlewareData, placement, strategy } = await FloatingUIDOM.computePosition(this.tooltipTriggerElm, tooltipTextEle, obj );

    if(!this.placement){
      this.placement = placement;
      this.changeDetector.detectChanges();
    }

    let updatedValues = this.setManualArrowPosition(x,y);

    Object.assign(tooltipTextEle.style, {
        left: `${updatedValues.x}px`,
        top: `${updatedValues.y}px`
    });

    if (middlewareData.arrow) {
        if((this.placement == 'right-start' || this.placement == 'left-start') && tooltipTextEle.offsetHeight < 60){
          middlewareData.arrow.y = middlewareData.arrow.y - 3;
        } else if((this.placement == 'right-end' || this.placement == 'left-end') && tooltipTextEle.offsetHeight < 60){
          middlewareData.arrow.y = middlewareData.arrow.y + 3;
        }
        Object.assign(arrowElement.style, {
            left: `${middlewareData.arrow.x ?? 0}px`,
            top: `${middlewareData.arrow.y ?? 0}px`
        });
    }
  }

  /**
   * @description handle the tooltip arrow position
   * @private
   * @memberof TooltipComponent
   */
  private setManualArrowPosition(x, y){
    if(this.setArrowPosition){
      let placement = this.placement?.split('-')[0];

      switch(placement){
        case 'top':
          y = y + this.setArrowPosition;
          break;
        case 'bottom':
          y = y - this.setArrowPosition;
          break;
        case 'left':
          x = x + this.setArrowPosition;
          break;
        case 'right':
          x = x - this.setArrowPosition;
          break;
      }
    }
    return {x, y};
  }

}
