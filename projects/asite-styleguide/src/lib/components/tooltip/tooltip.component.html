    <div class="tooltip-wrapper" [ngClass]="panelClass">
        <div id="tooltipText" #tooltipTextElm *ngIf="showTooltip" (mouseenter)="isShowOnHover && tooltipMouseEnter()" (mouseleave)="isShowOnHover && tooltipMouseLeave()" [ngClass]="[placement]" [class.error]="isError" class="tooltip-text-container" [style.width]="customWidth">
            <span [innerHTML]="formattedTooltipText"></span>
            <svg id="arrow-ele" #arrowElm [ngClass]="arrowPlacement" class="arrow" width="10" height="14" viewBox="0 0 10 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0.999999 8.66023C-0.333334 7.89043 -0.333333 5.96593 1 5.19613L10 -2.18391e-05L10 13.8564L0.999999 8.66023Z" fill="#071529"/>
            </svg>
        </div>
    </div>