@import "../../../../assets/scss/styles.scss";

.tooltip-wrapper{
    
    .tooltip-text-container{
        font-family: $fontFamily;
        z-index: 99999999 !important;
        background-color: #071529;
        border: 1px solid #071529;
        color: white;
        position: absolute;
        padding: 6px 10px;
        border-radius: 6px;
        font-size: 14px;
        word-wrap: break-word;
        transition: opacity 0.2s ease-in-out;

        &.error{
            background-color: #720000;
            border: 1px solid #720000;
            svg path{
                fill: #720000;
            }
        }

        &.deg90, &.bottom, &.bottom-end, &.bottom-start{
            #arrow-ele{
                top: -10px !important;
                left: auto !important;
                transform: rotate(90deg);         }
        }

        &.bottom-end, &.top-end{
            #arrow-ele{
                right: 15px;
            }
        }

        &.deg180, &.left, &.left-end, &.left-start{
            #arrow-ele{
                right: -10px !important;
                left: auto !important;
                transform: rotate(180deg);
            }
        }

        &.deg270, &.top, &.top-end, &.top-start{
            #arrow-ele{
                bottom: -10px !important;
                top: auto !important;
                left: auto !important;
                transform: rotate(270deg);
            }
        }
        
        &.deg360, &.right, &.right-end, &.right-start{
            #arrow-ele{
                left: -10px !important;
                transform: rotate(360deg);
            }
        }

        #arrow-ele {
            position: absolute;
        }
    }
}