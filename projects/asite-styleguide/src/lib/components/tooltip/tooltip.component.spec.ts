import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { TooltipComponent } from './tooltip.component';
import { ChangeDetectorRef, Component, ViewChild } from '@angular/core';
import { By } from '@angular/platform-browser';

@Component({
  template: `
            <button #triggerbtn class="triggerbtn">Hover me</button>
            <lib-tooltip tooltipText="Test Tooltip" isShowOnHover = "true" isShowOnClick="true" [tooltipTriggerElm]='triggerbtn'>
            </lib-tooltip>`
})
class TestHostComponent {
  @ViewChild(TooltipComponent) tooltipComponent!: TooltipComponent;
}

describe('TooltipComponent', () => {
  let component: TooltipComponent;
  let fixture: ComponentFixture<TestHostComponent>;
  let hostComponent: TestHostComponent;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TestHostComponent],
      imports: [TooltipComponent],
      providers: [ChangeDetectorRef]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(TestHostComponent);
    hostComponent = fixture.componentInstance;
    fixture.detectChanges();
    component = hostComponent.tooltipComponent;
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should set tooltipText input', () => {
    expect(component.tooltipText).toBe('Test Tooltip');
  });

  it('should show tooltip on mouse enter and when component.isShowOnHover = true and hide on mouse leave', fakeAsync(() => {
    component.isShowOnHover = true;
    fixture.detectChanges();
    const triggerElement = fixture.debugElement.query(By.css('.triggerbtn'));
    triggerElement.nativeElement.dispatchEvent(new Event('mouseenter'));
    expect(component.showTooltip).toBeTrue();

    triggerElement.nativeElement.dispatchEvent(new Event('mouseleave'));
    tick(250);
    fixture.detectChanges();
    expect(component.showTooltip).toBeFalse();
  }));

  it('should toggle tooltip on click', () => {
    fixture.detectChanges();
    const triggerElement = fixture.debugElement.query(By.css('.triggerbtn'));
    triggerElement.nativeElement.dispatchEvent(new Event('click'));
    fixture.detectChanges();
    expect(component.showTooltip).toBeTrue();

    triggerElement.nativeElement.dispatchEvent(new Event('click'));
    fixture.detectChanges();
    expect(component.showTooltip).toBeFalse();
  });

  it('should keep tooltip open when mouse enters tooltip', fakeAsync(() => {
    component.isShowOnHover = true;
    component.showTooltip = true;
    fixture.detectChanges();
    const tooltipElement = fixture.debugElement.query(By.css('#tooltipText'));
    tooltipElement.nativeElement.dispatchEvent(new Event('mouseenter'));
    tick(50);
    fixture.detectChanges();
    expect(component.showTooltip).toBeTrue();
  }));

  it('should hide tooltip when mouse leaves tooltip', fakeAsync(() => {
    component.isShowOnHover = true;
    component.showTooltip = true;
    fixture.detectChanges();
    
    const tooltipElement = fixture.debugElement.query(By.css('#tooltipText'));
    tooltipElement.nativeElement.dispatchEvent(new Event('mouseleave'));
    tick(250);
    fixture.detectChanges();
    expect(component.showTooltip).toBeFalse();
  }));

  it('should manually adjust arrow position', () => {
    component.setArrowPosition = 10;
    component.placement = 'top';
    const position = component['setManualArrowPosition'](100, 200);
    expect(position.y).toBe(210);
  });

  it('should manually adjust arrow position - bottom', () => {
    component.setArrowPosition = 10;
    component.placement = 'bottom';
    const position = component['setManualArrowPosition'](100, 200);
    expect(position.y).toBe(190);
    expect(position.x).toBe(100);
  });

  it('should manually adjust arrow position - left', () => {
    component.setArrowPosition = 10;
    component.placement = 'left';
    const position = component['setManualArrowPosition'](100, 200);
    expect(position.x).toBe(110);
    expect(position.y).toBe(200);
  });

  it('should manually adjust arrow position - right', () => {
    component.setArrowPosition = 10;
    component.placement = 'right';
    const position = component['setManualArrowPosition'](100, 200);
    expect(position.x).toBe(90);
    expect(position.y).toBe(200);
  });

  it('should call directTooltipHandler when show input is set', () => {
    const spy = spyOn<any>(component, 'directTooltipHandler');
    component.show = true;
    expect(spy).toHaveBeenCalledWith(true);
  });

  it('should update tooltip position when isShow is true', () => {
    component['directTooltipHandler'](true);
    expect(component['showTooltip']).toBeTrue();
  });
});
