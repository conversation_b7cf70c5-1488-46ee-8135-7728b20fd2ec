@import '../../../../assets/scss/styles.scss';

.main{
    width: fit-content;
    #product-tray {
        width: fit-content;
        background-color: var(--lib-color-gray-7, #FAFAFA);
        color: var(--lib-color-gray, #616161);
        font-family: $fontFamily;
        .product-list {
            display: flex;
            align-items: center;
            list-style: none;
            padding: 0;
            margin: 0;
            &.cde {
                .product-item {
                    &:hover{
                        background-color: var(--lib-color-purple-6, #CFCDF4);
                    }
                    &.active {
                        color: var(--lib-color-white, #ffffff);
                        background-color: var(--lib-color-tertiary, #1d1878);
                    }
                }
            }
            &.marketplace {
                .product-item {
                    &:hover{
                        background-color: var(--lib-color-gray-6, #E9E9E9);
                    }
                    &.active {
                        color: var(--lib-color-white,#ffffff);
                        background-color: var(--lib-color-black, #0C1315);
                    }
                }
            }
            &.architects {
                .product-item {
                    &:hover{
                        background-color: var(--lib-color-green-7, #D8FCF3);
                    }
                    &.active {
                        color: var(--lib-color-white, #ffffff);
                        background-color: var(--lib-color-green-2, #1C6F5A);
                    }
                }
            }
            .product-item {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 10px 14px 9px 14px;
                border-left: 2px solid var(--lib-color-gray-5, #C5C5C5);
                cursor: pointer;
                &:first-child {
                    border: none;
                }
                i {
                    font-size: 22px;
                }
                .product-label {
                    margin-top: 3px;
                    font-weight: 400;
                    font-size: 16px;
                }
                &.more-btn{
                    gap: 5px;
                    .product-label {
                        margin: 0;
                    }
                }
                &:hover{
                    background-color: var(--lib-color-purple-6, #CFCDF4);
                }
                &.active{
                    color: var(--lib-color-white, #ffffff);
                    background-color: var(--lib-color-tertiary, #1d1878);
                    border: none;

                    + .product-item{
                        border: none;
                    }
                }
                &.disabled{
                    color: var(--lib-color-gray-4,#8D8D8D);
                    cursor: not-allowed;
                    background: none;
                    &:hover,
                    &.active {
                        color: var(--lib-color-gray-4,#8D8D8D);
                    }
                }
                &.right{
                    flex-direction: row-reverse;
                }
            }
        }
    }
}