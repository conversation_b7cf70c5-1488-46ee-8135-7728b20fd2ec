<div class="main" *ngIf="productList">
    <div id="product-tray">
        <ul [ngClass]="'product-list ' + activeProductId">
            <ng-container *ngFor="let product of productList, index as productIndex">
                <li tabindex="0" (click)="switchProduct(product)" *ngIf="productIndex < maxProductCount && product.selected"
                    class="product-item"
                    [ngClass]="product.id === activeProductId ? 'active ' + iconPosition : iconPosition"
                    [class.disabled]="product.disable">
                    <i [class]="product.icon" *ngIf="product.icon"></i>
                    <span class="product-label">
                        {{product.label}}
                    </span>
                </li>
            </ng-container>
            <li *ngIf="productList?.length > maxProductCount" class="product-item more-btn">
                <i class="iconoir-plus"></i>
            </li>
        </ul>
    </div>
</div>