import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProductData, ProductIconPositionEnum } from './products-tray.model';


@Component({
  selector: 'lib-products-tray',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './products-tray.component.html',
  styleUrls: ['./products-tray.component.scss']
})
export class ProductsTrayComponent {

  constructor() { }

  @Input('productList') productList: ProductData[] = [];

  @Input('activeProductId') activeProductId: string;

  @Input('minProductCount') minProductCount: number = 1;

  @Input('maxProductCount') maxProductCount: number = 5;

  @Input('iconPosition') iconPosition: ProductIconPositionEnum = ProductIconPositionEnum.Left;

  // Switch Product and Emit Switched product
  switchProduct(product:ProductData) {
    if (!product.disable) {
      this.activeProductId = product.id;
      this.productSelected.emit(product);
    }
  }

  @Output()
  productSelected = new EventEmitter<ProductData>();
}