import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ProductsTrayComponent } from './products-tray.component';
import { ProductData, ProductIconEnum, ProductIdEnum } from './products-tray.model';

describe('ProductsTrayComponent', () => {
  let component: ProductsTrayComponent;
  let fixture: ComponentFixture<ProductsTrayComponent>;

  beforeEach(async () => {
    fixture = TestBed.createComponent(ProductsTrayComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default Button Group data as an empty array', () => {
    expect(component.productList).toEqual([]);
  });

  it('should set activeProductId', () => {
    const activeProductId = 'cde';
    component.activeProductId = activeProductId;
    expect(component.activeProductId).toEqual(activeProductId);
  });

  it('should emit productSelected event when a Product is selected from product-tray', () => {
    const mockProduct: ProductData = {
      id: ProductIdEnum.CDE,
      uiIndex: 1,
      disable: false,
      selected: true,
      label: "CDE",
      icon: ProductIconEnum.CDE,
      commingSoon: false,
      subTabs: []
    };
    spyOn(component.productSelected, 'emit');

    component.switchProduct(mockProduct);

    expect(component.productSelected.emit).toHaveBeenCalledWith(mockProduct);
  });
});
