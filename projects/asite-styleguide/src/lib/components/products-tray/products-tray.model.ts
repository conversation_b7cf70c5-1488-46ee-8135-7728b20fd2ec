export interface ProductData {
    id: string,
    icon: string;
    label: string;
    uiIndex: number;
    selected: boolean;
    disable: boolean;
    commingSoon: boolean;
    subTabs: string[];
}

export enum ProductIdEnum {
    CDE = 'cde',
    APM = 'apm',
    Marketplace = 'marketplace',
    Architects = 'architects',
    AsiteField = 'asite_field',
    BidWinner = 'bid_winner',
    ProjectControls = 'project_controls',
    Sustainability = 'sustainability'
}

export enum ProductIconEnum {
    CDE = 'iconoir-database',
    APM = 'iconoir-journal',
    Marketplace = 'iconoir-shop-four-tiles',
    Architects = 'iconoir-frame-select',
    AsiteField = 'iconoir-ruler-arrows',
    BidWinner = 'iconoir-leaderboard',
    ProjectControls = 'iconoir-ios-settings',
    Sustainability = 'iconoir-orange-slice'
}

export enum ProductIconPositionEnum {
    Left = 'left',
    Right = 'right',
}