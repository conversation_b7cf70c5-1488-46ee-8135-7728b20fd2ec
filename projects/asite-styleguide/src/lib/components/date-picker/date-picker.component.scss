@import '../../../../assets/scss/styles.scss';

.datepicker-container {
  display: flex;
  width: 15rem;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 1px;
  font-family: $fontFamily;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  position: relative;

  .date-picker-label {
    display: flex;
    gap: 2px;
    margin-bottom: 10px;

    .required {
      color: var(--lib-color-red-2, #8E0000);
    }
  }

  span {
    color: var(--lib-color-black, #0C1315);
    font-size: 16px;
    line-height: 16px;
  }

  &.disabled {
    cursor: not-allowed;
  }

  .datepicker-contents {
    font-family: $fontFamily;
    width: 100%;
    max-width: 20rem;
    height: 2.625rem;
    display: flex;
    align-items: center;
    color: var(--lib-color-black, #0C1315);
    background-color: var(--lib-color-gray-7, #FAFAFA);
    border: 1px solid var(--lib-color-gray, #616161);
    text-align: left;
    gap: 8px;
    overflow: hidden;
    border-radius: 8px;
    box-sizing: border-box;
    justify-content: space-between;
    margin: 0 auto;

    &:hover {
      border: 1px solid var(--lib-color-tertiary, #1d1878);
    }

    &.disabled {
      background: var(--lib-color-gray-6, #E9E9E9);
      color: var(--lib-color-gray-4, #8D8D8D);
      pointer-events: none;
      .mat-datepicker-input.inputBox{
        color: var(--lib-color-gray, #616161) !important;
      }
    }

    &.error {
      border-color: var(--lib-color-red-2, #8e0000);
    }

    .inputContainer {
      display: contents;

      .iconoir-calendar {
        padding-left: 5px;
        position: absolute;
      }

      .inputBox {
        font-family: $fontFamily;
        color: var(--lib-color-black, #0C1315);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        padding: 13px 0px 13px 30px;
        justify-content: center;
        align-items: center;
        background-color: transparent;
        border: none;
        width: inherit;
        flex-grow: 1;
        overflow: scroll;
        box-shadow: none;

        &:focus {
          outline: none;
        }
        &::placeholder {
          color: var(--lib-color-gray, #616161);
        }
      }
      

      .iconoir-calendar,
      .iconoir-xmark {
        font-size: 20px;
        cursor: pointer;
      }

      input[type="text" i] {
        cursor: pointer;
      }
    }
  }
  .datepicker-contents.disabled{
    .mat-date-range-input-separator{
      color: var(--lib-color-gray, #616161) !important;
    }
  }
  .mat-date-range-input-inner[disabled]{
    color: var(--lib-color-gray, #616161) !important;
  }
}

.error-msg {
  display: flex;
  align-items: center;
  gap: 7px;
  margin-top: 8px;
  color: var(--lib-color-red-1, #720000);
  font-family: $fontFamily;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;

  .iconoir-warning-circle {
    font-size: 20px;
    margin-left: auto;
  }
}