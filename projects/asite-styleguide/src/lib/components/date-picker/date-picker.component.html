<div class="datepicker-container" [style.width]="width" [ngClass]="{ disabled: disabled, required: isRequired }">
  <div class="date-picker-label" *ngIf="label || isRequired">
    <span class="label" *ngIf="label">{{ label }}</span>
    <span class="required" *ngIf="isRequired && label">*</span>
  </div>
  <div class="datepicker-contents" [ngClass]="{ disabled: disabled, required: isRequired }">
    <div class="inputContainer">
      <i *ngIf="iconLeft" class="iconoir-calendar"></i>

      <!-- Single Date Picker -->
      <ng-container *ngIf="!isRangePickerMode">
        <input [attr.id]="label" [min]="restrictPastDates ? today : null" class="inputBox" [matDatepicker]="picker" [placeholder]="dateFormatString" [formControl]="serializedDate"
          readonly [required]="isRequired" [disabled]="disabled" (click)="picker.open()"
          (dateChange)="onDateChange($event)" />
        <mat-datepicker #picker [panelClass]="'custom-datepicker'" [opened]="isOpen"
          [calendarHeaderComponent]="DatePickerHeaderBarComponent" (opened)="onDatePickerOpen(picker)" (closed)="onDatePickerClose(picker)">
        </mat-datepicker>
      </ng-container>

      <!-- Date Range Picker -->
      <ng-container *ngIf="isRangePickerMode">
        <mat-date-range-input [formGroup]="range" [rangePicker]="rangePicker" readonly [disabled]="disabled"
          (click)="rangePicker.open()">
          <input matStartDate formControlName="start" readonly [placeholder]="dateFormatString"
            [required]="isRequired" />
          <input matEndDate formControlName="end" readonly [required]="isRequired"
            (dateChange)="onRangeChange($event)" />
        </mat-date-range-input>
        <mat-date-range-picker #rangePicker [panelClass]="'custom-datepicker'"
          [calendarHeaderComponent]="DatePickerHeaderBarComponent">
        </mat-date-range-picker>
      </ng-container>
    </div>
  </div>
</div>
<div *ngIf="(serializedDate.hasError('required') && !isRangePickerMode) || (range.controls.start.hasError('required') && isRangePickerMode) || (range.controls.end.hasError('required') && isRangePickerMode)"
  class="error-msg" [style.width]="width">
  <mat-error *ngIf="serializedDate.hasError('required') && !isRangePickerMode">Date is required</mat-error>
  <mat-error *ngIf="range.controls.start.hasError('required') && isRangePickerMode">Start date is required</mat-error>
  <mat-error *ngIf="range.controls.end.hasError('required') && isRangePickerMode">End date is required</mat-error>
  <i class="iconoir-warning-circle"></i>
</div>