<div class="date-picker-header">
    <div class="date-picker-month">
        <i id="month-arrow-left" class="iconoir-nav-arrow-left" (click)="previousClicked('month')"></i>
        <span class="date-picker-month-label" (click)="monthClick()">{{ periodLabelMonth }}</span>
        <i id="month-arrow-right" class="iconoir-nav-arrow-right" (click)="nextClicked('month')"></i>
    </div>
    <div class="date-picker-year">
        <i id="year-arrow-left" class="iconoir-nav-arrow-left" (click)="previousClicked(_calendar.currentView === 'multi-year' ? 'multi-year' : 'year')"></i>
        <span class="date-picker-year-label" (click)="yearClick()">{{ periodLabelYear }}</span>
        <i id="year-arrow-right" class="iconoir-nav-arrow-right" (click)="nextClicked(_calendar.currentView === 'multi-year' ? 'multi-year' : 'year')"></i>
    </div>
</div>