import { Component } from '@angular/core';
import { DateAdapter } from '@angular/material/core';
import { MatCalendar } from '@angular/material/datepicker';

@Component({
  selector: 'lib-date-picker-header-bar',
  standalone: true,
  imports: [],
  templateUrl: './date-picker-header-bar.component.html',
  styleUrl: './date-picker-header-bar.component.scss'
})
export class DatePickerHeaderBarComponent<D> {

  constructor(
    public _calendar: MatCalendar<D>,
    public _dateAdapter: DateAdapter<D>,
  ) { }

  /**
   * @description Get label for the current period (e.g., month name).
   * @memberof DatePickerHeaderComponent
   */
  get periodLabelMonth() {
    return this._dateAdapter.format(
      this._calendar.activeDate,
      'MMMM'
    );
  }
  
  /**
   * @description Get label for the current period (e.g., year).
   * @memberof DatePickerHeaderComponent
   */
  get periodLabelYear() {
    return this._dateAdapter.format(
      this._calendar.activeDate,
      'yyyy'
    );
  }  

  /**
   * @description Handle click event for switching to month view.
   * @memberof DatePickerHeaderComponent
   */
  monthClick() {
    this._calendar.currentView = 'year';
  }

  /**
   * @description Handle click event for switching to year view.
   * @memberof DatePickerHeaderComponent
   */
  yearClick() {
    this._calendar.currentView = 'multi-year';
  }

  /**
   * @description Handle click event for navigating to previous period (month or year).
   * @memberof DatePickerHeaderComponent
   */
  previousClicked(mode: 'month' | 'year' | 'multi-year') {
    if (mode === 'multi-year') {
      this._calendar.activeDate = this._dateAdapter.addCalendarYears(this._calendar.activeDate, -24);
    } else {
      this._calendar.activeDate =
        mode === 'month'
          ? this._dateAdapter.addCalendarMonths(this._calendar.activeDate, -1)
          : this._dateAdapter.addCalendarYears(this._calendar.activeDate, -1);
    }
  }

  /**
   * @description Handle click event for navigating to next period (month or year).
   * @memberof DatePickerHeaderComponent
   */
  nextClicked(mode: 'month' | 'year' | 'multi-year') {
    if (mode === 'multi-year') {
      this._calendar.activeDate = this._dateAdapter.addCalendarYears(this._calendar.activeDate, 24);
    } else {
      this._calendar.activeDate =
        mode === 'month'
          ? this._dateAdapter.addCalendarMonths(this._calendar.activeDate, 1)
          : this._dateAdapter.addCalendarYears(this._calendar.activeDate, 1);
    }
  }
}
