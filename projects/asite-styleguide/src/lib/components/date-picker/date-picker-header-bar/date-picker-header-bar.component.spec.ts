import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DatePickerHeaderBarComponent } from './date-picker-header-bar.component';
import { MatCalendar } from '@angular/material/datepicker';
import { DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';

describe('DatePickerHeaderBarComponent', () => {
  let component: DatePickerHeaderBarComponent<Date>;
  let fixture: ComponentFixture<DatePickerHeaderBarComponent<Date>>;

  const testDateFormats = {
    parse: {
      dateInput: 'DD-MM-YYYY',
    },
    display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'MMM YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'MMMM YYYY',
    },
  };

  const dateAdapterSpy = jasmine.createSpyObj('DateAdapter', ['format', 'addCalendarYears', 'addCalendarMonths']);

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DatePickerHeaderBarComponent],
      providers: [
        { provide: DateAdapter, useValue: dateAdapterSpy },
        { provide: MAT_DATE_FORMATS, useValue: testDateFormats },
        MatCalendar
      ]
    })
    .compileComponents();
  });


  beforeEach(() => {
    fixture = TestBed.createComponent(DatePickerHeaderBarComponent<Date>);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  
  it('should set currentView to "year" on clicking month label', () => {
    const monthLabelElement = fixture.debugElement.nativeElement.querySelector('.date-picker-month-label');
    monthLabelElement.click();
    fixture.detectChanges();
    expect(component['_calendar'].currentView).toBe('year');
  });

  it('should set currentView to "multi-year" on clicking year label', () => {
    const yearLabelElement = fixture.debugElement.nativeElement.querySelector('.date-picker-year-label');
    yearLabelElement.click();
    fixture.detectChanges();
    expect(component['_calendar'].currentView).toBe('multi-year');
  });


  it('should change the current month to previous month', () => {
    const monthLabelElement = fixture.debugElement.nativeElement.querySelector('#month-arrow-left');
    monthLabelElement.click();
    fixture.detectChanges();
    expect(dateAdapterSpy.addCalendarMonths).toHaveBeenCalledWith(component._calendar.activeDate, -1);
  });

  it('should change the current month to next month', () => {
    const monthLabelElement = fixture.debugElement.nativeElement.querySelector('#month-arrow-right');
    monthLabelElement.click();
    fixture.detectChanges();
    expect(dateAdapterSpy.addCalendarMonths).toHaveBeenCalledWith(component._calendar.activeDate, 1);
  });

  it('should change the current year to previous year', () => {
    component['_calendar'].currentView = 'year';
    fixture.detectChanges();
    const monthLabelElement = fixture.debugElement.nativeElement.querySelector('#year-arrow-left');
    monthLabelElement.click();
    fixture.detectChanges();
    expect(dateAdapterSpy.addCalendarYears).toHaveBeenCalledWith(component._calendar.activeDate, -1);
  });

  it('should change the current year to next year', () => {
    component['_calendar'].currentView = 'year';
    fixture.detectChanges();
    const monthLabelElement = fixture.debugElement.nativeElement.querySelector('#year-arrow-right');
    monthLabelElement.click();
    fixture.detectChanges();
    expect(dateAdapterSpy.addCalendarYears).toHaveBeenCalledWith(component._calendar.activeDate, 1);
  });

  it('should change the year selection to previous 24 years', () => {
    component['_calendar'].currentView = 'multi-year';
    fixture.detectChanges();
    const monthLabelElement = fixture.debugElement.nativeElement.querySelector('#year-arrow-left');
    monthLabelElement.click();
    fixture.detectChanges();
    expect(dateAdapterSpy.addCalendarYears).toHaveBeenCalledWith(component._calendar.activeDate, -24);
  });

  it('should change the year selection to next 24 years', () => {
    component['_calendar'].currentView = 'multi-year';
    fixture.detectChanges();
    const monthLabelElement = fixture.debugElement.nativeElement.querySelector('#year-arrow-right');
    monthLabelElement.click();
    fixture.detectChanges();
    expect(dateAdapterSpy.addCalendarYears).toHaveBeenCalledWith(component._calendar.activeDate, 24);
  });
});
