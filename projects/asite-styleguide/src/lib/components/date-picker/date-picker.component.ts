import { Component, EventEmitter, Inject, Input, Output, ViewChild, ChangeDetectionStrategy } from '@angular/core';
import { MatDatepicker, MatDatepickerModule } from '@angular/material/datepicker';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE, NativeDateAdapter } from '@angular/material/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { DatePickerHeaderBarComponent } from './date-picker-header-bar/date-picker-header-bar.component';
import dayjs from 'dayjs';
import 'dayjs/locale/en';
import 'dayjs/locale/fr';
import 'dayjs/locale/es';
import 'dayjs/locale/ru';
import 'dayjs/locale/zh-cn';
import 'dayjs/locale/de';
import 'dayjs/locale/ga';
import 'dayjs/locale/ja';
import 'dayjs/locale/ar';
import 'dayjs/locale/it';
import 'dayjs/locale/nl';
import 'dayjs/locale/cs';
import 'dayjs/locale/tr';
import 'dayjs/locale/vi';
import customParseFormat from 'dayjs/plugin/customParseFormat';

dayjs.extend(customParseFormat); // Enable custom parse format

// Custom date format configuration
const CustomDateFormat = {
  parse: { dateInput: 'MM/DD/YYYY' },
  display: {
    dateInput: 'MM/DD/YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

export class CustomDateAdapter extends NativeDateAdapter {
  private readonly dateFormatString: string = 'dd-M-yy'; //en_GB default date format.
  
  override format(date: Date, displayFormat: any): string {
    const dayjsDate = dayjs(date);
    const formatToUse = displayFormat || this.dateFormatString;
    const formattedDate = dayjsDate.format(formatToUse);
    if (formatToUse.includes('yyyy')) {
      const yearAsNumber = date.getFullYear();
      return formattedDate.replace('yyyy', String(yearAsNumber));
    }
    return formattedDate;
  }
}

@Component({
  selector: 'lib-date-picker',
  standalone: true,
  providers: [
    { provide: MAT_DATE_FORMATS, useValue: CustomDateFormat },
    { provide: DateAdapter, useClass: CustomDateAdapter, deps: [MAT_DATE_LOCALE] },
  ],
  imports: [MatDatepickerModule, CommonModule, ReactiveFormsModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: './date-picker.component.html',
  styleUrl: './date-picker.component.scss'
})
export class DatePickerComponent {

  DatePickerHeaderBarComponent = DatePickerHeaderBarComponent;

  /**
  * @description Show the label.
  * @memberof DatePickerComponent
  */
  @Input() label: string = '';

  /**
  * @description Flag to display calendar icon on the left.
  * @memberof DatePickerComponent
  */
  @Input() iconLeft: boolean = true;

  /**
   * @description Date Format used as placeholder for datepicker input.
   * @memberof DatePickerComponent
   */
  @Input() dateFormatString: string = 'dd-M-yy';

  /**
  * @description used to change labels of the datepicker.
  * @memberof DatePickerComponent
  */
  @Input() dateLocale: string = 'en_GB';

  /**
   * @description Custom width of the datepicker component.
   * @memberof DatePickerComponent
   */
  @Input() customWidth: string = '';

  /**
   * @description Flag to enable or disable the datepicker.
   * @memberof DatePickerComponent
   */
  @Input() disabled: boolean = false;

  /**
   * @description Flag to make Required the datepicker.
   * @memberof DatePickerComponent
   */
  @Input() isRequired: boolean = false;

  /**
   * @description Event emitted when the date is changed.
   * @memberof DatePickerComponent
   */
  @Output() dateChangedEvent = new EventEmitter<any>();

  /**
   * @description Flag to control the datepicker's open state.
   * @memberof DatePickerComponent
   */
  @Input() isOpen: boolean = false;

  /**
  * @description Flag to change to Range Date picker.
  * @memberof DatePickerComponent
  */
  @Input() isRangePickerMode: boolean = false;

  /**
  * @description Flag to disable past date in single date picker.
  * @memberof DatePickerComponent
  */
  @Input() restrictPastDates: boolean = false;  


  /**
   * @description Reference to the MatDatepicker component.
   * @memberof MatDatepicker
   */
  @ViewChild(MatDatepicker, { static: false }) datePicker: MatDatepicker<Date>;
  @ViewChild('rangePicker', { static: false }) rangePicker: any;

  /**
   * @description Form control for the serialized date value.
   * @memberof DatePickerComponent
   */
  serializedDate = new FormControl<Date | null>(null);
  range = new FormGroup({
    start: new FormControl<Date | null>(null),
    end: new FormControl<Date | null>(null),
  });

  private _defaultSelectedDate = '';
  private _defaultSelectedRange: { start: string; end: string } | null = null;

  /**
   * @description Default date value setter.
   * @memberof DatePickerComponent
   */
  @Input('defaultDate') set defaultDate(val: string | { start: string, end: string }) {
    if (typeof val === 'string') {
      this._defaultSelectedDate = val;
      this.setDefaultDate();
    } else {
      this._defaultSelectedRange = val;
      this.setDefaultRange();
    }
  }
   /**
   * @description Min date for date picker.
   * @memberof DatePickerComponent
   */
  public today = new Date();

  /**
   * @description Returns the custom width or default width of the datepicker.
   * @memberof DatePickerComponent
   */
  get width(): string {
    return this.customWidth || '300px';
  }

  constructor(@Inject(MAT_DATE_FORMATS) private data: any, private dateAdapter: DateAdapter<Date>) { }

  /**
   * @description Initialize component with locale and date format settings.
   * @memberof DatePickerComponent
   */
  ngOnInit() {
    //TODO: Commented as making issue while using into the filter-date
    // this.dateFormatString = this._convertDateFormat(this.dateFormatString);
    this.dateLocale = this._getDayJsLocale(this.dateLocale);
    dayjs.locale(this.dateLocale); // Set the Day.js locale
    this.overrideGetDayOfWeekNames();
    this.dateAdapter.setLocale(this.dateLocale);
    this.data.parse.dateInput = this.dateFormatString;
    this.data.display.dateInput = this.dateFormatString;
  }

  /**
   * @description Set default date after the view has initialized.
   * @memberof DatePickerComponent
   */
  ngAfterViewInit() {
    if (this.isRangePickerMode) {
      this.setDefaultRange();
    } else {
      this.setDefaultDate();
    }

    if (this.isOpen) {
      if (this.isRangePickerMode && this.rangePicker) {
        this.rangePicker.open();
      } else if (this.datePicker) {
        this.datePicker.open();
      }
    }
  }

  /**
   * @description Set the default date in the datepicker.
   * @memberof DatePickerComponent
   */
  private setDefaultDate() {
    if (this._defaultSelectedDate) {
      const parsedDate = dayjs(this._defaultSelectedDate, this.dateFormatString).toDate();
      this.serializedDate.setValue(parsedDate);
    }
  }

  /**
   * @description Set the default date range in the datepicker.
   * @memberof DatePickerComponent
   */
  private setDefaultRange() {
    if (this._defaultSelectedRange) {
      const start = dayjs(this._defaultSelectedRange.start, this.dateFormatString).toDate();
      const end = dayjs(this._defaultSelectedRange.end, this.dateFormatString).toDate();
      this.range.setValue({ start, end });
    }
  }

  /**
   * @description Handle datepicker open event.
   * @memberof DatePickerComponent
   */
  onDatePickerOpen(picker: MatDatepicker<Date>) {
    this.isOpen = true;
    if (picker) {
      picker.open();
    }
  }

  /**
   * @description Handle datepicker close event.
   * @memberof DatePickerComponent
   */
  onDatePickerClose(picker: MatDatepicker<Date>) {
    this.isOpen = false;
    if (picker) {
      picker.close();
    }
  }

  /**
   * @description Emit date change event when the date is selected.
   * @memberof DatePickerComponent
   */
  onDateChange(event: any) {
    if (event?.value) {
      const formattedDate = dayjs(event.value).format(this.dateFormatString);
      this.dateChangedEvent.emit(formattedDate);
    }
  }

  /**
   * @description Emit date range change event when the date range is selected.
   * @memberof DatePickerComponent
   */
  onRangeChange(event: any) {
    const { start, end } = this.range.value;
    const formattedStart = dayjs(start).format(this.dateFormatString);
    const formattedEnd = dayjs(end).format(this.dateFormatString);
    this.dateChangedEvent.emit({ start: formattedStart, end: formattedEnd });
  }

  /**
 * @description Overrides the getDayOfWeekNames method of the dateAdapter to customize day names.
 * It modifies the behavior to return abbreviated day names ('Sun', 'Mon', etc.) when style is 'narrow'.
 * @memberof DatePickerComponent
 * @private
 */
  private overrideGetDayOfWeekNames() {
    const originalGetDayOfWeekNames = this.dateAdapter.getDayOfWeekNames.bind(this.dateAdapter);
    this.dateAdapter.getDayOfWeekNames = (style: 'long' | 'short' | 'narrow'): string[] => {
      if (style === 'narrow') {
        style = 'short';
      }
      return originalGetDayOfWeekNames(style);
    };
  }

  /**
   * @description Convert Date Format To as support the angular material.
   * @type {dateFormat}
   * @memberof DatePickerComponent
   */
  private _convertDateFormat(dateFormat: string) {
    let matDateFormat: string = dateFormat;
    for (let i of dateFormat) {
      switch (i) {
        case 'd':
          matDateFormat = matDateFormat.replace('d', 'D');
          break;
        case 'm':
          matDateFormat = matDateFormat.replace('m', 'M');
          break;
        case 'M':
          matDateFormat = matDateFormat.replace('M', 'MMM');
          break;
        case 'y':
          matDateFormat = matDateFormat.replace('y', 'YY');
          break;
      }
    }
    return matDateFormat;
  }

  private _getDayJsLocale(inputLocale: string): string {
    const localeMapping: { [key: string]: string } = {
      "en_GB": "en-gb",
      "fr_FR": "fr",
      "es_ES": "es",
      "ru_RU": "ru",
      "en_AU": "en-au",
      "en_CA": "en-ca",
      "en_US": "en",
      "zh_CN": "zh-cn",
      "de_DE": "de",
      "ga_IE": "ga",
      "en_ZA": "en",
      "ja_JP": "ja",
      "ar_SA": "ar",
      "en_IE": "en-ie",
      "it_IT": "it",
      "nl_NL": "nl",
      "cs_CZ": "cs",
      "tr_TR": "tr",
      "vi_VN": "vi"
    };
    return localeMapping[inputLocale] || 'en'; // Default to 'en' if not found
  }
}
