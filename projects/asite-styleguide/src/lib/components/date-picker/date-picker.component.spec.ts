import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { Mat<PERSON><PERSON><PERSON><PERSON>, MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule, DateAdapter, MAT_DATE_FORMATS } from '@angular/material/core';
import { DatePickerComponent } from './date-picker.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ChangeDetectorRef } from '@angular/core';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import dayjs from 'dayjs';
import { By } from '@angular/platform-browser';
import { DatePickerHeaderBarComponent } from './date-picker-header-bar/date-picker-header-bar.component';

describe('DatePickerComponent', () => {
  let component: DatePickerComponent;
  let fixture: ComponentFixture<DatePickerComponent>;

  const testDateFormats = {
    parse: {
      dateInput: 'DD-MM-YYYY',
    },
    display: {
      dateInput: 'DD-MM-YYYY',
      monthYearLabel: 'MMM YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'MMMM YYYY',
    },
  };

  beforeEach(async () => {
    const dateAdapterSpy = jasmine.createSpyObj('DateAdapter', ['setLocale', 'getDayOfWeekNames']);

    await TestBed.configureTestingModule({
      imports: [
        DatePickerComponent,
        DatePickerHeaderBarComponent,
        MatDatepickerModule,
        MatNativeDateModule,
        FormsModule,
        ReactiveFormsModule,
        NoopAnimationsModule
      ],
      providers: [
        { provide: DateAdapter, useValue: dateAdapterSpy },
        { provide: MAT_DATE_FORMATS, useValue: testDateFormats },
        { provide: ChangeDetectorRef, useValue: { markForCheck: () => { } } }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DatePickerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should open date picker when isOpen is true', fakeAsync(() => {
    component.isOpen = true;
    component.ngAfterViewInit();
    fixture.detectChanges();
    tick();
    const datePicker = fixture.debugElement.query(By.directive(MatDatepicker)).componentInstance;
    expect(datePicker.opened).toBeTrue();
  }));

  it('should have default value of false for disabled property', () => {
    expect(component.disabled).toBe(false);
  });

  it('should set disabled property to true', () => {
    component.disabled = true;
    expect(component.disabled).toBe(true);
  });

  it('should handle setting disabled property to false', () => {
    component.disabled = true;
    component.disabled = false;
    expect(component.disabled).toBe(false);
  });

  it('should handle datepicker open event', () => {
    const datePicker = fixture.debugElement.query(By.directive(MatDatepicker)).componentInstance;
    component.onDatePickerOpen(datePicker);
    expect(component.isOpen).toBeTrue();
  });

  it('should handle datepicker close event', () => {
    const datePicker = fixture.debugElement.query(By.directive(MatDatepicker)).componentInstance;
    component.onDatePickerClose(datePicker);
    expect(component.isOpen).toBeFalse();
  });

  it('should call setDefaultRange when isRangePickerMode is true', fakeAsync(() => {
    spyOn(component as any, 'setDefaultRange');
    component.isRangePickerMode = true;
    component.ngAfterViewInit();
    tick();
    fixture.detectChanges();
    expect((component as any).setDefaultRange).toHaveBeenCalled();
  }));

  it('should call setDefaultDate when isRangePickerMode is false', fakeAsync(() => {
    spyOn(component as any, 'setDefaultDate');
    component.isRangePickerMode = false;
    component.ngAfterViewInit();
    tick();
    fixture.detectChanges();
    expect((component as any).setDefaultDate).toHaveBeenCalled();
  }));

  it('should open rangePicker when isOpen and isRangePickerMode are true', fakeAsync(() => {
    component.isOpen = true;
    component.isRangePickerMode = true;
    component.rangePicker = { open: jasmine.createSpy('open') };
    component.ngAfterViewInit();
    tick();
    fixture.detectChanges();
    expect(component.rangePicker.open).toHaveBeenCalled();
  }));

  it('should set the correct width', () => {
    component.customWidth = '400px';
    expect(component.width).toBe('400px');
    component.customWidth = '';
    expect(component.width).toBe('300px');
  });

  it('should convert date format correctly', () => {
    const inputFormat = 'dd-MM-yyyy';
    const expectedFormat = 'DD-MMMMMM-YYYYYYYY';
    const result = component['_convertDateFormat'](inputFormat);

    expect(result).toBe(expectedFormat);
  });

  it('should convert lowercase m to uppercase M', () => {
    const inputFormat = 'yy-m-d';
    const expectedFormat = 'YYYY-M-D';
    const result = component['_convertDateFormat'](inputFormat);

    expect(result).toBe(expectedFormat);
  });

  it('should set date format string correctly', () => {
    const newFormat = 'YYYY-MM-DD';
    component.dateFormatString = newFormat;
    expect(component.dateFormatString).toBe(newFormat);
  });

  it('should initialize date locale correctly', () => {
    component.dateLocale = 'fr_FR';
    component.ngOnInit();
    expect(dayjs.locale()).toBe('fr');
  });

  it('should emit correct formatted date when date changes', () => {
    spyOn(component.dateChangedEvent, 'emit');
    component.serializedDate.setValue(dayjs('2023-10-01', 'YYYY-MM-DD').toDate());
    component.onDateChange({ value: component.serializedDate.value });
    expect(component.dateChangedEvent.emit).toHaveBeenCalledWith('Su-10-yy');
  });

  it('should emit correct formatted date range when range changes', () => {
    spyOn(component.dateChangedEvent, 'emit');

    const startDate = dayjs('2023-10-01', 'YYYY-MM-DD').toDate();
    const endDate = dayjs('2023-10-05', 'YYYY-MM-DD').toDate();

    component.range.setValue({ start: startDate, end: endDate });
    component.onRangeChange({});

    const expectedStart = dayjs(startDate).format(component.dateFormatString);
    const expectedEnd = dayjs(endDate).format(component.dateFormatString);

    expect(component.dateChangedEvent.emit).toHaveBeenCalledWith({
      start: expectedStart,
      end: expectedEnd
    });
  });

  it('should set the default date and call setDefaultDate when input is a string', () => {
    // Arrange
    const testDate = '01-10-2023';
    spyOn(component as any, 'setDefaultDate');

    // Act
    component.defaultDate = testDate;

    // Assert
    expect(component['_defaultSelectedDate']).toBe(testDate);
    expect((component as any).setDefaultDate).toHaveBeenCalled();
  });

  it('should set the default range and call setDefaultRange when input is an object', () => {
    // Arrange
    const testRange = { start: '01-10-2023', end: '05-10-2023' };
    spyOn(component as any, 'setDefaultRange');

    // Act
    component.defaultDate = testRange;

    // Assert
    expect(component['_defaultSelectedRange']).toEqual(testRange);
    expect((component as any).setDefaultRange).toHaveBeenCalled();
  });

  it('should set serializedDate when defaultDate is set with a string', () => {
    // Arrange
    component.dateFormatString = 'DD-MM-YYYY';
    component.defaultDate = '15-08-2023';

    // Act
    fixture.detectChanges();

    // Assert
    const expectedDate = dayjs('15-08-2023', component.dateFormatString).toDate();
    expect(component.serializedDate.value).toEqual(expectedDate);
  });

  it('should set range when defaultDate is set with a range', () => {
    // Arrange
    component.dateFormatString = 'DD-MM-YYYY';
    component.defaultDate = { start: '01-07-2023', end: '10-07-2023' };

    // Act
    fixture.detectChanges();

    // Assert
    const expectedStart = dayjs('01-07-2023', component.dateFormatString).toDate();
    const expectedEnd = dayjs('10-07-2023', component.dateFormatString).toDate();
    expect(component.range.value).toEqual({ start: expectedStart, end: expectedEnd });
  });
});