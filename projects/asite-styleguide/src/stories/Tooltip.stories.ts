import { StoryFn, Meta, moduleMetadata } from '@storybook/angular';
import { TooltipComponent } from '../lib/components/tooltip/tooltip.component';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '../lib/components';

export default {
  title: 'Components/Tooltip',
  component: TooltipComponent,
  tags: ['autodocs'],
  decorators: [
      moduleMetadata({
        imports: [ButtonComponent]
      }),
    ],
} as Meta;

const DefaultTemplate: StoryFn<TooltipComponent> = (args: TooltipComponent) => ({
  component: TooltipComponent,
  props: args,
  template:`
    <div style="width:min-content" #triggerbtn >
        <lib-button [label]="'Hover on button for tooltip'" ></lib-button>
    </div>

    <lib-tooltip
      [tooltipText]="tooltipText"
      [tooltipTriggerElm]="triggerbtn"
      [customWidth]="customWidth"
      [isShowOnClick]="isShowOnClick"
      [isShowOnHover]="isShowOnHover" >
    </lib-tooltip>
  `,
});

export const Default = DefaultTemplate.bind({});
Default.args = {
  tooltipText: 'this is example of custom tooltip.',
  customWidth: '150px',
  isShowOnHover: true,
  isShowOnClick: false,
};

const ErrorTemplate: StoryFn<TooltipComponent> = (args: TooltipComponent) => ({
  component: TooltipComponent,
  props: args,
  template:`
    <div style="width:min-content" #triggerbtn >
        <lib-button [label]="'Hover on button for error tooltip'" ></lib-button>
    </div>
    
    <lib-tooltip
      [tooltipText]="tooltipText"
      [tooltipTriggerElm]="triggerbtn"
      [customWidth]="customWidth"
      [isShowOnClick]="isShowOnClick"
      [isShowOnHover]="isShowOnHover"
      [isError]='isError'>
    </lib-tooltip>
  `,
});
export const Error = ErrorTemplate.bind({});

Error.args = {
  tooltipText: 'this is example for error tooltip',
  customWidth: '150px',
  isShowOnHover: true,
  isShowOnClick: false,
  isError:true
};

const PersistentTemplate: StoryFn<TooltipComponent> = (args: TooltipComponent) => ({
  props: args,
  template: `
    <div style="width:min-content" #triggerbtn >
        <lib-button [customWidth]="500" [label]="'Button with persistent tooltip'" ></lib-button>
    </div>
    <lib-tooltip
      [tooltipText]="tooltipText"
      [tooltipTriggerElm]="triggerbtn"
      [isShowOnClick]="isShowOnClick"
      [isShowOnHover]="isShowOnHover"
      [show]='show'>
    </lib-tooltip>
  `,
});
export const PersistentTooltip = PersistentTemplate.bind({});

PersistentTooltip.args = {
  tooltipText: 'this is example of persistent tooltip',
  show:true,
};

const MouseTemplate: StoryFn<TooltipComponent> = (args: TooltipComponent) => ({
  props: args,
  template: `
    <div style="width:min-content" #triggerbtn >
        <lib-button [label]="'Click on button to show the tooltip'" ></lib-button>
    </div>

    <lib-tooltip
      [tooltipText]="tooltipText"
      [tooltipTriggerElm]="triggerbtn"
      [customWidth]="customWidth"
      [isShowOnClick]="isShowOnClick"
      [isShowOnHover]="isShowOnHover"
      [placement]='placement' >
    </lib-tooltip>
  `,
});
export const MouseClick = MouseTemplate.bind({});

MouseClick.args = {
  tooltipText: 'This is an example of tooltip shown on button click',
  customWidth: '150px',
  isShowOnHover: false,
  isShowOnClick: true,
  placement: 'bottom-end',
  isError: false
};




