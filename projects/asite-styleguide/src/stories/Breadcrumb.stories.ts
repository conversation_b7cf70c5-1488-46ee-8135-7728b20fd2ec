import { StoryFn, Meta } from '@storybook/angular';
import { BreadcrumbComponent } from '../lib/components/breadcrumb/breadcrumb.component';

export default {
  title: 'Components/Breadcrumb',
  component: BreadcrumbComponent,
  tags: ['autodocs'],
  argTypes: {
    breadcrumbItems: {
      label: 'Label',
      level: 'HOME',
    },
    // Actions
    breadcrumbClicked: {action: 'Breadcrumb Clicked'}
  },
} as Meta;

const Template: StoryFn<BreadcrumbComponent> = (args: BreadcrumbComponent) => ({
  component: BreadcrumbComponent,
  props: args,
});

export const Default = Template.bind({});
Default.args = {
  breadcrumbItems: [
    {label: 'Breadcrumb', level: 'Level 1'},
    {label: 'Breadcrumb', level: 'Level 2'},
    {label: 'Breadcrumb', level: 'Level 3'},
  ],
}

export const With_Icons = Template.bind({});
With_Icons.args = {
  breadcrumbItems: [
    {label: 'Breadcrumb', level: 'Level 1', icon: 'iconoir-lens'},
    {label: 'Breadcrumb', level: 'Level 2', icon: 'iconoir-lens'},
    {label: 'Breadcrumb', level: 'Level 3', icon: 'iconoir-lens'},
  ],
}