import { StoryFn, Meta } from '@storybook/angular';
import { SidebarComponent } from '../lib/components/sidebar/sidebar.component';
import { delay, of } from 'rxjs';

export default {
  title: 'Components/Sidebar',
  component: SidebarComponent,
  tags: ['autodocs'],
  decorators: [
    (storyFn) => {
      // Only inject once
      const styleId = 'sidebar-style';
      if (!document.getElementById(styleId)) {
        const style = document.createElement('style');
        style.id = styleId;
        style.innerHTML = `
          .demo-sidebar {
            height: 60vh !important;
          }
        `;
        document.head.appendChild(style);
      }

      return storyFn();
    },
  ],
} as Meta;

const Template: StoryFn<SidebarComponent> = (args: SidebarComponent) => ({
  component: SidebarComponent,
  props: args
});

const fetchItems = (item) => {
  return of([          {
    id: 'subModule1',
    label: 'Module',
    icon: '',
    expanded: false,
    expandable: false,
    fetchFromRemote: false,
    isFetching: false,
    isFetched: false
  },
  {
    id: 'subModule2',
    label: 'Module',
    icon: '',
    expanded: false,
    expandable: false,
    fetchFromRemote: false,
    isFetching: false,
    isFetched: false
  }]).pipe(
    delay(1000) // one second delay
  );
};

export const Default = Template.bind({});
Default.args = {
  title: "Project Name",
  width: "200px",
  gap: "11px",
  panelClass: "demo-sidebar",
  fetchItems:fetchItems,
  menuItems: [
      {
        'id': 'module1',
        'label': 'Module',
        'icon': 'iconoir-lens',
        'expanded': false,
        'expandable': false,
        'fetchFromRemote': false,
        'isFetching': false,
        'isFetched': false,
      },
      {
        'id': 'module2',
        'label': 'Module',
        'icon': 'iconoir-lens',
        'expanded': false,
        'expandable': true,
        'fetchFromRemote': false,
        'isFetching': false,
        'isFetched': true,
        items: [
          {
            id: 'subModule1',
            label: 'Module',
            icon: '',
            expanded: false,
            expandable: false,
            fetchFromRemote: false,
            isFetching: false,
            isFetched: false
          },
          {
            id: 'subModule2',
            label: 'Module',
            icon: '',
            expanded: false,
            expandable: false,
            fetchFromRemote: false,
            isFetching: false,
            isFetched: false
          }
        ]
      }
    ],
};

export const FetchChildrenUsingApi = Template.bind({});
FetchChildrenUsingApi.args = {
  title: "Project Name",
  width: "200px",
  gap: "11px",
  panelClass: "demo-sidebar",
  fetchItems,
  menuItems: [
    {
      'id': 'module1',
      'label': 'Module',
      'icon': 'iconoir-lens',
      'expanded': false,
      'expandable': false,
      'fetchFromRemote': false,
      'isFetching': false,
      'isFetched': false,
    },
    {
      'id': 'module2',
      'label': 'Module',
      'icon': 'iconoir-lens',
      'expanded': false,
      'expandable': true,
      'fetchFromRemote': true,
      'isFetching': false,
      'isFetched': false,
      items: []
    }
  ]
};

const fetchItemsWithLazyLoad = (item) => {
  return of({
    data:
    [{
    id: 'subModule1',
    label: 'Module1',
    icon: '',
    expanded: false,
    expandable: false,
    fetchFromRemote: false,
    isFetching: false,
    isFetched: false
    },
    {
      id: 'subModule2',
      label: 'Module2',
      icon: '',
      expanded: false,
      expandable: false,
      fetchFromRemote: false,
      isFetching: false,
      isFetched: false
    },
    {
      id: 'subModule3',
      label: 'Module3',
      icon: '',
      expanded: false,
      expandable: false,
      fetchFromRemote: false,
      isFetching: false,
      isFetched: false
    },
    {
      id: 'subModule4',
      label: 'Module4',
      icon: '',
      expanded: false,
      expandable: false,
      fetchFromRemote: false,
      isFetching: false,
      isFetched: false
    },
    {
      id: 'subModule5',
      label: 'Module5',
      icon: '',
      expanded: false,
      expandable: false,
      fetchFromRemote: false,
      isFetching: false,
      isFetched: false
    },
    {
      id: 'subModule6',
      label: 'Module6',
      icon: '',
      expanded: false,
      expandable: false,
      fetchFromRemote: false,
      isFetching: false,
      isFetched: false
    },
    {
      id: 'subModule7',
      label: 'Module7',
      icon: '',
      expanded: false,
      expandable: false,
      fetchFromRemote: false,
      isFetching: false,
      isFetched: false
    },
    {
      id: 'subModule8',
      label: 'Module8',
      icon: '',
      expanded: false,
      expandable: false,
      fetchFromRemote: false,
      isFetching: false,
      isFetched: false
    }],
    hasNextPage: true
}).pipe(
    delay(1000) // one second delay
  );
};

export const FetchChildrenUsingApiWithLazyLoading = Template.bind({});
FetchChildrenUsingApiWithLazyLoading.args = {
  title: "Project Name",
  width: "200px",
  gap: "11px",
  isLazyLoadSupport: true,
  panelClass: "demo-sidebar",
  fetchItems: fetchItemsWithLazyLoad,
  menuItems: [
    {
      'id': 'module1',
      'label': 'Module',
      'icon': 'iconoir-lens',
      'expanded': false,
      'expandable': false,
      'fetchFromRemote': false,
      'isFetching': false,
      'isFetched': false,
    },
    {
      'id': 'module2',
      'label': 'Module',
      'icon': 'iconoir-lens',
      'expanded': false,
      'expandable': true,
      'fetchFromRemote': true,
      'isFetching': false,
      'isFetched': false,
      items: []
    }
  ]
};






