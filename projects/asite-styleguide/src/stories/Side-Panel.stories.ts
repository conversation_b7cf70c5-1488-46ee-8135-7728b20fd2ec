import { StoryFn, Meta, moduleMetadata } from '@storybook/angular';
import { ButtonComponent, SidePanelComponent } from '../lib/components/index';
import { CommonModule } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

export default {
  title: 'Components/Side-Panel',
  component: SidePanelComponent,
  tags: ['autodocs'],
  decorators: [
    moduleMetadata({
      imports: [CommonModule, ButtonComponent, BrowserAnimationsModule],
    }),
  ],
  argTypes: {
    title: {
      name: 'Title',
      control: 'text',
      defaultValue: 'Title',
    },
    customWidth: {
      name: 'customWidth',
      control: 'text',
      defaultValue: '40%'
    },
    customResizeWidth: {
      name: 'customResizeWidth',
      control: 'text',
      defaultValue: '70%'
    },
    opened: {
      name: 'opened',
      control: 'boolean',
      defaultValue: 'false'
    },
    onCloseSidePanel: {
      table: {
        defaultValue: { summary: 'EventEmitter<Event>()' },
      },
    },
    onResizeSidePanel: {
      table: {
        defaultValue: { summary: 'EventEmitter<Event>()' },
      },
    },
  },
  // Actions
  onCloseSidePanel: { action: 'Side Panel Closed Event' },
  onResizeSidePanel: { action: 'Side Panel Resized Event' }
} as Meta;

const Template: StoryFn<SidePanelComponent> = (args: SidePanelComponent) => ({
  component: SidePanelComponent,
  props: args,
  template: `
    <lib-button [label]="'Toggle Side Panel'" (click)="opened = !opened"></lib-button>
    <lib-side-panel 
      [title]="title" 
      [(opened)]="opened" 
      [customWidth]="customWidth" 
      [customResizeWidth]="customResizeWidth" 
      (onCloseSidePanel)="opened = false;">
    </lib-side-panel>
 `,
});

export const Default = Template.bind({});
Default.args = {
  title: 'Title',
  customWidth: '40%',
  customResizeWidth: '70%',
  opened: false
}

export const Title = Template.bind({});
Title.args = {
  title: 'Custom Side Panel Title',
  customWidth: '40%',
  customResizeWidth: '70%',
  opened: false
};

export const Width = Template.bind({});
Width.args = {
  title: 'Title',
  customWidth: '50%',
  customResizeWidth: '80%',
  opened: false
}

export const Body: StoryFn<SidePanelComponent> = (args: SidePanelComponent) => ({
  props: args,
  template: `
    <lib-button [label]="'Toggle Side Panel'" (click)="opened = !opened"></lib-button>
    <lib-side-panel 
      [title]="title" 
      [(opened)]="opened" 
      [customWidth]="customWidth" 
      [customResizeWidth]="customResizeWidth" 
      (onCloseSidePanel)="opened = false;">
      <div class="side-panel-content">
        <p> This is side panel body.</p>
      </div>
    </lib-side-panel>
 `,
});
Body.args = {
  title: 'Title',
  customWidth: '40%',
  customResizeWidth: '70%',
  opened: false
}

export const Actions: StoryFn<SidePanelComponent> = (args: SidePanelComponent) => ({
  props: args,
  template: `
    <lib-button [label]="'Toggle Side Panel'" (click)="opened = !opened"></lib-button>
    <lib-side-panel 
      [title]="title" 
      [(opened)]="opened" 
      [customWidth]="customWidth" 
      [customResizeWidth]="customResizeWidth" 
      (onCloseSidePanel)="opened = false;">
      <div class="side-panel-actions" style="display: flex; justify-content: space-between; gap: 10px;">
        <lib-button
          [iconLeft]="'iconoir-info-circle'" 
          [buttonType]="'secondary'" 
          [size]="'md'" 
          role="button">
        </lib-button>
        <lib-button
          [iconLeft]="'iconoir-download'" 
          [buttonType]="'secondary'" 
          [size]="'md'" 
          role="button">
        </lib-button>
      </div>
      <div class="side-panel-content">
        <p> This is side panel body.</p>
      </div>
    </lib-side-panel>
  `,
});
Actions.args = {
  title: 'Title',
  customWidth: '40%',
  customResizeWidth: '70%',
  opened: false
}
