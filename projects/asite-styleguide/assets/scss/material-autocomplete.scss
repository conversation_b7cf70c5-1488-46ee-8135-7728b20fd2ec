//this styling is to override the current angular material css in the component matautocomplete which is used in textbox storybook component
.asite-mat-option.mat-mdc-option .mdc-list-item__primary-text {
    display: flex;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-family: var(--font-family, 'Sofia Pro');
  }
  .cdk-overlay-container div.mat-mdc-autocomplete-panel {
    -ms-overflow-style: none; /* for Internet Explorer, Edge */
    scrollbar-width: none; /* for Firefox */
    overflow-y: scroll;

    &::-webkit-scrollbar {
      display: none; /* for Chrome, Safari, and Opera */
    }
    border-radius: 8px;
  }

  .cdk-overlay-container
    .cdk-overlay-pane:not(.mat-mdc-autocomplete-panel-above)
    div.mat-mdc-autocomplete-panel {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }

  .asite-mat-option.mat-mdc-option.mdc-list-item--selected:not(
      .mdc-list-item--disabled
    )
    .mdc-list-item__primary-text {
      color: var(--lib-color-black, #0C1315);
      font-family: var(--font-family, 'Sofia Pro');
  }

  .asite-mat-option
    .mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after {
    display: none;
  }