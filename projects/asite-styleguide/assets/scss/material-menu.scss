// this styling is to override the current angular material css in the component matmenu which is used in select storybook component.
.customizeMenu.mat-mdc-menu-panel {
    max-width: 100%;
}

.customizeMenu .mat-mdc-menu-content {

    padding: 0;
    max-height: 350px;
    overflow-x: hidden;

    .dropdown-list {
        width: inherit;
        background-color: var(--lib-color-white, #fff);
        z-index: 999;
        box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
        border-radius: 8px;
        cursor: pointer;
        -ms-overflow-style: none; /* for Internet Explorer, Edge */
        scrollbar-width: none; /* for Firefox */
        max-height: 350px;
        overflow-y: scroll;
        
        &::-webkit-scrollbar {
          display: none; /* for Chrome, Safari, and Opera */
        }
    
        .dd-option {
            padding: 4px 8px;
            background-color: var(--lib-color-white, #fff);
            font-family: var(--font-family, 'Sofia Pro');
    
            p {
                font-size: 14px;
                font-weight: 400;
                padding: 14px 12px;
                border-radius: 10px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
        
                &:hover {
                    background: var(--lib-color-purple-6, #cfcdf4);
                }

                &.dropdown-image-text {
                    display: flex;
                    align-items: center;
                    gap: 8px;

                    img {
                        width: 20px;
                        height: 20px;
                        object-fit: contain;
                    }
                }
            }

            .isSelected, .optionSelected {
                background: var(--lib-color-purple-6, #cfcdf4);
            }
        }

        .dd-option:nth-child(1 of .dd-option:not([disabled])) p {
            background: var(--lib-color-purple-6, #cfcdf4);
        }

        .mat-mdc-menu-item-text {
            width: 100%;
            
            p {
                font-family: var(--font-family, 'Sofia Pro');
            }
        }
    }

    .mat-mdc-menu-item:not([disabled]).cdk-program-focused p, 
    .mat-mdc-menu-item:not([disabled]).cdk-keyboard-focused p, 
    .mat-mdc-menu-item:not([disabled]).mat-mdc-menu-item-highlighted p {
        font-size: 14px;
        font-weight: 400;
        padding: 14px 12px;
        border-radius: 10px;
        background: var(--lib-color-purple-6, #cfcdf4);
    }

}