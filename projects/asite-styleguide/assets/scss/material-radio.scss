.asite-ui-radio {
  &.mat-mdc-radio-button .mdc-form-field {
    font-family: var(--font-family, 'Sofia Pro');
    font-size: 14px;
    font-weight: 400;
    display: flex;
    align-items: center;
  }

  &.mat-mdc-radio-button .mdc-form-field>label,
  &.mat-mdc-radio-button .mdc-radio {
    padding-left: 0;
  }

  &.mat-mdc-radio-button.mat-accent {
    --mat-select-trigger-text-font: var(--font-family, 'Sofia Pro');
    --mdc-radio-disabled-selected-icon-opacity: 0.8;
    --mdc-radio-disabled-unselected-icon-opacity: 0.8;
    --mdc-radio-selected-focus-icon-color: var(--lib-color-primary, #4940D7);
    --mdc-radio-selected-hover-icon-color: var(--lib-color-primary, #4940D7);
    --mdc-radio-selected-icon-color: var(--lib-color-primary, #4940D7);
    --mdc-radio-selected-pressed-icon-color: var(--lib-color-primary, #4940D7);
    --mat-radio-checked-ripple-color: unset !important;
    --mat-ripple-color: unset !important;
    --mdc-radio-disabled-selected-icon-color: var(--lib-color-disabled, #848484);
    --mdc-radio-disabled-unselected-icon-color: var(--lib-color-gray-5, #C5C5C5);
    --mdc-radio-unselected-pressed-icon-color: var(--lib-color-primary, #4940D7);
    --mdc-radio-unselected-hover-icon-color: var(--lib-color-primary, #4940D7);
    --mdc-radio-unselected-icon-color: var(--lib-color-primary, #4940D7);
    --mdc-radio-unselected-focus-icon-color: var(--lib-color-primary, #4940D7);
  }

  &.mat-mdc-radio-button.mat-mdc-radio-checked .mdc-radio__background::before,
  &.mat-mdc-radio-button.mat-mdc-radio-checked:hover .mdc-radio__background::before,
  &.mat-mdc-radio-button .mdc-radio .mdc-radio__background::before,
  &.mat-mdc-radio-button .mdc-radio:hover .mdc-radio__background::before,
  &.mat-mdc-radio-button.mat-mdc-radio-checked .mat-ripple-element {
    background: none;
  }

  &.mat-mdc-radio-button .mat-ripple-element {
    background-color: unset;
  }


  &.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle {
    background-color: var(--mdc-radio-selected-icon-color);
  }

  &.mat-mdc-radio-button .mdc-radio--disabled+label {
    color: var(--lib-color-black, #0C1315);
  }

  &.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle,
  &.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle {
    border: 2px solid var(--mdc-radio-disabled-unselected-icon-color);
    opacity: unset;
  }

  &.mat-mdc-radio-button .mdc-radio [aria-disabled=true] .mdc-radio__native-control:checked+.mdc-radio__background .mdc-radio__outer-circle,
  &.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled:checked+.mdc-radio__background .mdc-radio__outer-circle {
    border: 2px solid var(--mdc-radio-disabled-selected-icon-color);
    opacity: unset;
  }

  &.mat-mdc-radio-button .mdc-radio .mdc-radio__native-control:disabled+.mdc-radio__background .mdc-radio__inner-circle {
    opacity: unset;
    background-color: var(--mdc-radio-disabled-selected-icon-color);
  }
}
