/* panel styling */

.panel {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-radius: 12px;
    border: 1px solid var(--lib-color-gray-6, #E9E9E9);
}

.panel-header {
    display: flex;
    padding: 27px 30px 26px 30px;
    justify-content: center;
    align-items: center;
    gap: 12px;
    align-self: stretch;
    border-bottom: 1px solid var(--lib-color-gray-6, #E9E9E9);
}

.panel-footer {
    display: flex;
    padding: 16px 30px;
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    align-self: stretch;
    border-radius: 0px 0px 4px 4px;
    border-top: 1px solid var(--lib-color-gray-6, #E9E9E9);
}