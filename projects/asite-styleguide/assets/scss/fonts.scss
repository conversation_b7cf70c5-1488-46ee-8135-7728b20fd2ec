:root {
    --font-size: 16px; // Default font size in pixels
    --font-family: '"Sofia Pro", Arial';
}

$fontFamily: '"Sofia Pro", Arial';
$fontSize: 16px;

// Default font URL that can be overridden by other projects
$fontUrl: '/assets/fonts/Sofia-Pro-Regular-Az.otf' !default;

@font-face {
    font-family: $fontFamily;
    // src: url('../fonts/Sofia Pro Regular Az.otf') format('opentype');
    // src: url(/assets/fonts/Sofia-Pro-Regular-Az.otf) format('opentype');
    src: url($fontUrl) format('opentype');
    font-style: normal;
}

// @font-face {
//     font-family: $fontFamily;
//     // src: url('../fonts/Sofia-Pro-UltraLight-Az.otf') format('opentype');
//     src: local('Sofia Pro UltraLight AZ'), local('Sofia-Pro-UltraLight-Az'), url(/assets/fonts/Sofia-Pro-UltraLight-Az.otf) format('opentype');
//     font-weight: 100;
//     font-style: normal;
// }

// @font-face {
//     font-family: $fontFamily;
//     // src: url('../fonts/Sofia Pro ExtraLight Az.otf') format('opentype');
//     src: local('Sofia Pro ExtraLight Az'), local('Sofia-Pro-ExtraLight-Az'), url(/assets/fonts/Sofia-Pro-ExtraLight-Az.otf) format('opentype');
//     font-weight: 200;
//     font-style: normal;
// }

// @font-face {
//     font-family: $fontFamily;
//     // src: url('../fonts/Sofia Pro Light Az.otf') format('opentype');
//     src: local('Sofia Pro Light Az'), local('Sofia-Pro-Light-Az'), url(/assets/fonts/Sofia-Pro-Light-Az.otf) format('opentype');
//     font-weight: 300;
//     font-style: normal;
// }

// @font-face {
//     font-family: $fontFamily;
//     // src: url('../fonts/Sofia Pro Regular Az.otf') format('opentype');
//     src: local('Sofia Pro Regular Az'), local('Sofia-Pro-Regular-Az'), url(/assets/fonts/Sofia-Pro-Regular-Az.otf) format('opentype');
//     font-weight: 400;
//     font-style: normal;
// }

// @font-face {
//     font-family: $fontFamily;
//     // src: url('../fonts/Sofia Pro Medium Az.otf') format('opentype');
//     src: local('Sofia Pro Medium Az'), local('Sofia-Pro-Medium-Az'), url(/assets/fonts/Sofia-Pro-Medium-Az.otf) format('opentype');
//     font-weight: 500;
//     font-style: normal;
// }

// @font-face {
//     font-family: $fontFamily;
//     // src: url('../fonts/Sofia Pro Semi Bold Az.otf') format('opentype');
//     src: local('Sofia Pro Semi Bold Az'), local('Sofia-Pro-Semi-Bold-Az'), url(/assets/fonts/Sofia-Pro-Semi-Bold-Az.otf) format('opentype');
//     font-weight: 600;
//     font-style: normal;
// }

// @font-face {
//     font-family: $fontFamily;
//     // src: url('../fonts/Sofia Pro Bold Az.otf') format('opentype');
//     src: local('Sofia Pro Bold Az'), local('Sofia-Pro-Bold-Az'), url(/assets/fonts/Sofia-Pro-Bold-Az.otf) format('opentype');
//     font-weight: 700;
//     font-style: normal;
// }

// @font-face {
//     font-family: $fontFamily;
//     // src: url('../fonts/Sofia Pro Black Az.otf') format('opentype');
//     src: local('Sofia Pro Black Az'), local('Sofia-Pro-Black-Az'), url(/assets/fonts/Sofia-Pro-Black-Az.otf) format('opentype');
//     font-weight: 800;
//     font-style: normal;
// }