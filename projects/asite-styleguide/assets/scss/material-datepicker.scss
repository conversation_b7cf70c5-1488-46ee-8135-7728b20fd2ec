
.custom-datepicker .mat-calendar-body-cell-content.mat-focus-indicator.mat-calendar-body-selected{
    background-color:var(--lib-color-primary, #4940d7);
}

@media (hover: hover) {
    .custom-datepicker .mat-calendar-body-cell:not(.mat-calendar-body-disabled):hover>.mat-calendar-body-cell-content:not(.mat-calendar-body-selected):not(.mat-calendar-body-comparison-identical) {
        background-color: var(--lib-color-purple-6, #CFCDF4)!important; 
    }
}
.custom-datepicker .mat-calendar-body-cell-content.mat-focus-indicator.mat-calendar-body-today:not(.mat-calendar-body-selected){
    background-color: var(--lib-color-gray-6,#E9E9E9);
}
.custom-datepicker .mat-calendar-body-cell-content.mat-focus-indicator.mat-calendar-body-today{
    border:none;
}

.mat-datepicker-content {
    border-radius: 4px 4px 8px 8px !important;
}

.mat-calendar {
    font-family: var(--font-family, 'Sofia Pro') !important;
    font-size: 12px !important;
    font-weight: 400;
    text-align: center;
    font-style: normal;
    line-height: normal;
    color: var(--lib-color-gray, #616161);
}

.mat-calendar-table-header-divider {
    display: none;
}

.mat-calendar-body .mat-calendar-body-label {
    visibility: hidden;
    padding: 0px !important;
}

.mat-date-range-input {
    font-family: var(--font-family, 'Sofia Pro');
    color: var(--lib-color-gray, #616161);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    padding: 13px 0px 13px 30px;
}

.datepicker-contents.disabled{
    .mat-date-range-input {
        .mat-date-range-input-separator{
      color: var(--lib-color-gray, #616161) !important;
    }
    
    }

}

.mat-calendar-body-in-range::before {
    background: var(--lib-color-gray-6, #E9E9E9) !important;
}

.custom-datepicker .mat-calendar-body-range-start .mat-calendar-body-cell-content.mat-focus-indicator.mat-calendar-body-selected {
    border-radius: 20px 0px 0px 20px;
    background: var(--Products-Primary, #4940D7);
}

.custom-datepicker .mat-calendar-body-range-end .mat-calendar-body-cell-content.mat-focus-indicator.mat-calendar-body-selected {
    border-radius: 0px 20px 20px 0px;
    background: var(--Products-Primary, #4940D7);
}